{"__meta": {"id": "01K4P1CJT98147A36KMF4K7QGY", "datetime": "2025-09-09 09:28:02", "utime": **********.634051, "method": "GET", "uri": "/plugin/ProductSpecial/public/count-down/moment.min.js", "ip": "127.0.0.1"}, "php": {"version": "8.2.9", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1757381280.628695, "end": **********.634066, "duration": 2.00537109375, "duration_str": "2.01s", "measures": [{"label": "Booting", "start": 1757381280.628695, "relative_start": 0, "end": **********.600428, "relative_end": **********.600428, "duration": 1.****************, "duration_str": "1.97s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.600439, "relative_start": 1.****************, "end": **********.634067, "relative_end": 9.5367431640625e-07, "duration": 0.033627986907958984, "duration_str": "33.63ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.610537, "relative_start": 1.***************, "end": **********.626436, "relative_end": **********.626436, "duration": 0.015898942947387695, "duration_str": "15.9ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.63218, "relative_start": 2.****************, "end": **********.6323, "relative_end": **********.6323, "duration": 0.00011992454528808594, "duration_str": "120μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.632314, "relative_start": 2.****************, "end": **********.63233, "relative_end": **********.63233, "duration": 1.5974044799804688e-05, "duration_str": "16μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "30MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "GET plugin/{code}/{path}", "controller": "Beike\\Shop\\Http\\Controllers\\PluginController@asset<a href=\"phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FShop%2FHttp%2FControllers%2FPluginController.php&line=19\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "shop.plugin.asset", "file": "<a href=\"phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FShop%2FHttp%2FControllers%2FPluginController.php&line=19\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">beike/Shop/Http/Controllers/PluginController.php:19-34</a>"}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"data": {"status": "200 OK", "full_url": "http://newshop.shopleade.test/plugin/ProductSpecial/public/count-down/moment.min.js", "action_name": "shop.plugin.asset", "controller_action": "Beike\\Shop\\Http\\Controllers\\PluginController@asset", "uri": "GET plugin/{code}/{path}", "controller": "Beike\\Shop\\Http\\Controllers\\PluginController@asset<a href=\"phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FShop%2FHttp%2FControllers%2FPluginController.php&line=19\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FShop%2FHttp%2FControllers%2FPluginController.php&line=19\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">beike/Shop/Http/Controllers/PluginController.php:19-34</a>", "duration": "2.01s", "peak_memory": "32MB", "response": "application/javascript", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-839633121 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-839633121\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1519118036 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1519118036\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-720586895 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"898 characters\">_ss_s_uid=df387fd27bfaa61c2967bdd458dc2053; beike_version={%22current%22:%*********%22%2C%22latest%22:%221.5.6%22%2C%22release_date%22:%222025-05-08%22%2C%22has_new_version%22:true}; XSRF-TOKEN=eyJpdiI6IitMNWI3Znppc0VjR2JUUVpOM0d3UVE9PSIsInZhbHVlIjoiOTIvV1lwYWxPenhlc3V5M0xtR1RRZGRkdkQwVm1teDhwQU80SGtsZlhRZHBtVFhkOS92NHdZOTh6K2VpM1Z4VFRKS0dyYkJnUzZxbFdIc1FaQXFEbHl4Z1V4UFozRG80Z2xHQkhibnBDRVIram5jUGFTNEZGRWJQTGo1Z1h4RnIiLCJtYWMiOiI5YTAzZWI0OGE3MDA1MDExMzk3ZWMxNzQ1MzYxZGFkY2NjNTRlZmNiM2RiNGJmMGM2ZDI0NzAwYTJmMDhkYzQxIiwidGFnIjoiIn0%3D; shopleade_session=eyJpdiI6IlRzcm5tdHhaQXVvMFNCYVJZVWFOY3c9PSIsInZhbHVlIjoiWEhONkVYV3NLYlhkdGpyYURueGNzMjlDUG8ramdpNHMvUW15TDFaM3E3OUZjbmdMZWZ1NkZob2NyMzNNN2YyOElOTkN2SHF5bXIwYWppK1piOElSZXliOHE5SURoTWk1ZjE3RkhUWVJzL3RlQWdyM2FJeEptYmxGSlB0WTBaSDciLCJtYWMiOiI5MWM4YmMzOTA2NmY2NmE4Y2Q2NjZiNGI4OGJhYjFlNGExYmQwZTRkNGNmZjAxNDJjY2FmMDgxYzE4ZjYyZjk0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">zh-CN,zh;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">http://newshop.shopleade.test/product-seoccc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">newshop.shopleade.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-720586895\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-806043147 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_ss_s_uid</span>\" => \"<span class=sf-dump-str title=\"32 characters\">df387fd27bfaa61c2967bdd458dc2053</span>\"\n  \"<span class=sf-dump-key>beike_version</span>\" => \"<span class=sf-dump-str title=\"89 characters\">{&quot;current&quot;:&quot;*******&quot;,&quot;latest&quot;:&quot;1.5.6&quot;,&quot;release_date&quot;:&quot;2025-05-08&quot;,&quot;has_new_version&quot;:true}</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IitMNWI3Znppc0VjR2JUUVpOM0d3UVE9PSIsInZhbHVlIjoiOTIvV1lwYWxPenhlc3V5M0xtR1RRZGRkdkQwVm1teDhwQU80SGtsZlhRZHBtVFhkOS92NHdZOTh6K2VpM1Z4VFRKS0dyYkJnUzZxbFdIc1FaQXFEbHl4Z1V4UFozRG80Z2xHQkhibnBDRVIram5jUGFTNEZGRWJQTGo1Z1h4RnIiLCJtYWMiOiI5YTAzZWI0OGE3MDA1MDExMzk3ZWMxNzQ1MzYxZGFkY2NjNTRlZmNiM2RiNGJmMGM2ZDI0NzAwYTJmMDhkYzQxIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>shopleade_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IlRzcm5tdHhaQXVvMFNCYVJZVWFOY3c9PSIsInZhbHVlIjoiWEhONkVYV3NLYlhkdGpyYURueGNzMjlDUG8ramdpNHMvUW15TDFaM3E3OUZjbmdMZWZ1NkZob2NyMzNNN2YyOElOTkN2SHF5bXIwYWppK1piOElSZXliOHE5SURoTWk1ZjE3RkhUWVJzL3RlQWdyM2FJeEptYmxGSlB0WTBaSDciLCJtYWMiOiI5MWM4YmMzOTA2NmY2NmE4Y2Q2NjZiNGI4OGJhYjFlNGExYmQwZTRkNGNmZjAxNDJjY2FmMDgxYzE4ZjYyZjk0IiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-806043147\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-228260924 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 09 Sep 2025 01:28:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">application/javascript</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-228260924\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-981495424 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-981495424\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://newshop.shopleade.test/plugin/ProductSpecial/public/count-down/moment.min.js", "action_name": "shop.plugin.asset", "controller_action": "Beike\\Shop\\Http\\Controllers\\PluginController@asset"}, "badge": null}}