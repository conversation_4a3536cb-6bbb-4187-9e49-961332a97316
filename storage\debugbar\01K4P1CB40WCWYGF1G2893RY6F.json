{"__meta": {"id": "01K4P1CB40WCWYGF1G2893RY6F", "datetime": "2025-09-09 09:27:54", "utime": **********.753314, "method": "GET", "uri": "/carts/mini", "ip": "127.0.0.1"}, "php": {"version": "8.2.9", "interface": "cgi-fcgi"}, "messages": {"count": 9, "messages": [{"message": "HOOK === hook_filter: footer.content", "message_html": null, "is_string": true, "label": "log", "time": **********.469349, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === hook_filter: model.page.url", "message_html": null, "is_string": true, "label": "log", "time": **********.722107, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === hook_filter: model.page.url", "message_html": null, "is_string": true, "label": "log", "time": **********.722298, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === hook_filter: model.page.url", "message_html": null, "is_string": true, "label": "log", "time": **********.926674, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === hook_filter: model.page.url", "message_html": null, "is_string": true, "label": "log", "time": **********.926883, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === hook_filter: menu.content", "message_html": null, "is_string": true, "label": "log", "time": **********.927156, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === hook_filter: service.cart.list", "message_html": null, "is_string": true, "label": "log", "time": **********.33936, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === hook_filter: service.cart.data", "message_html": null, "is_string": true, "label": "log", "time": **********.746635, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === hook_filter: cart.mini_cart.data", "message_html": null, "is_string": true, "label": "log", "time": **********.748187, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}]}, "time": {"start": 1757381269.519822, "end": **********.753332, "duration": 5.2335100173950195, "duration_str": "5.23s", "measures": [{"label": "Booting", "start": 1757381269.519822, "relative_start": 0, "end": **********.620751, "relative_end": **********.620751, "duration": 2.***************, "duration_str": "2.1s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.620759, "relative_start": 2.****************, "end": **********.753333, "relative_end": 1.1920928955078125e-06, "duration": 3.****************, "duration_str": "3.13s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.626327, "relative_start": 2.****************, "end": **********.630978, "relative_end": **********.630978, "duration": 0.004651069641113281, "duration_str": "4.65ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.750839, "relative_start": 5.***************, "end": **********.751094, "relative_end": **********.751094, "duration": 0.0002551078796386719, "duration_str": "255μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.751909, "relative_start": 5.***************, "end": **********.751949, "relative_end": **********.751949, "duration": 4.00543212890625e-05, "duration_str": "40μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "31MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 1, "nb_templates": 1, "templates": [{"name": "1x cart.mini", "param_count": null, "params": [], "start": **********.747685, "type": "blade", "hash": "bladeD:\\shopleadeCont\\git\\saas\\themes\\default/cart/mini.blade.phpcart.mini", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fthemes%2Fdefault%2Fcart%2Fmini.blade.php&line=1", "ajax": false, "filename": "mini.blade.php", "line": "?"}, "render_count": 1, "name_original": "cart.mini"}]}, "route": {"uri": "GET carts/mini", "domain": "newshop.shopleade.test", "middleware": "shop", "controller": "Beike\\Shop\\Http\\Controllers\\CartController@miniCart<a href=\"phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FShop%2FHttp%2FControllers%2FCartController.php&line=158\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "shop.carts.mini", "prefix": "/", "file": "<a href=\"phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FShop%2FHttp%2FControllers%2FCartController.php&line=158\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">beike/Shop/Http/Controllers/CartController.php:158-170</a>"}, "queries": {"count": 16, "nb_statements": 16, "nb_visible_statements": 16, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 3.0787500000000003, "accumulated_duration_str": "3.08s", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select count(*) as aggregate from `saas_commissions` where `saas_store_id` = 88 and `status` = 1 and `total` >= '0.01' and `paid_at` is null", "type": "query", "params": [], "bindings": [88, 1, "0.01"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/CheckStoreExpired.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\CheckStoreExpired.php", "line": 29}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 119}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 805}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 784}], "start": **********.633065, "duration": 0.19735, "duration_str": "197ms", "memory": 0, "memory_str": null, "filename": "CheckStoreExpired.php:29", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/CheckStoreExpired.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\CheckStoreExpired.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fapp%2FHttp%2FMiddleware%2FCheckStoreExpired.php&line=29", "ajax": false, "filename": "CheckStoreExpired.php", "line": "29"}, "connection": "wintoshop", "explain": null, "start_percent": 0, "width_percent": 6.41}, {"sql": "select * from `languages` where `status` = 1 and `languages`.`store_id` = 88 order by `sort_order` asc", "type": "query", "params": [], "bindings": [1, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "beike/Repositories/LanguageRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\LanguageRepo.php", "line": 101}, {"index": 16, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 491}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 121}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 64}], "start": **********.833734, "duration": 0.1545, "duration_str": "155ms", "memory": 0, "memory_str": null, "filename": "LanguageRepo.php:101", "source": {"index": 15, "namespace": null, "name": "beike/Repositories/LanguageRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\LanguageRepo.php", "line": 101}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FRepositories%2FLanguageRepo.php&line=101", "ajax": false, "filename": "LanguageRepo.php", "line": "101"}, "connection": "wintoshop", "explain": null, "start_percent": 6.41, "width_percent": 5.018}, {"sql": "select * from `languages` where `status` = 1 and `languages`.`store_id` = 88 order by `sort_order` asc", "type": "query", "params": [], "bindings": [1, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "beike/Repositories/LanguageRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\LanguageRepo.php", "line": 101}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 45}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 25}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 19, "namespace": null, "name": "app/Http/Middleware/SetLocaleFromSession.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\SetLocaleFromSession.php", "line": 37}], "start": **********.99162, "duration": 0.19340000000000002, "duration_str": "193ms", "memory": 0, "memory_str": null, "filename": "LanguageRepo.php:101", "source": {"index": 15, "namespace": null, "name": "beike/Repositories/LanguageRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\LanguageRepo.php", "line": 101}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FRepositories%2FLanguageRepo.php&line=101", "ajax": false, "filename": "LanguageRepo.php", "line": "101"}, "connection": "wintoshop", "explain": null, "start_percent": 11.428, "width_percent": 6.282}, {"sql": "select * from `pages` where `active` = 1 and `pages`.`id` = 21 and `pages`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [1, 21, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 157}, {"index": 24, "namespace": null, "name": "beike/Repositories/FooterRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\FooterRepo.php", "line": 31}, {"index": 25, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 47}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 25}], "start": 1757381272.186546, "duration": 0.20341, "duration_str": "203ms", "memory": 0, "memory_str": null, "filename": "Url.php:68", "source": {"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FLibraries%2FUrl.php&line=68", "ajax": false, "filename": "Url.php", "line": "68"}, "connection": "wintoshop", "explain": null, "start_percent": 17.71, "width_percent": 6.607}, {"sql": "select * from `pages` where `active` = 1 and `pages`.`id` = 18 and `pages`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [1, 18, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 157}, {"index": 24, "namespace": null, "name": "beike/Repositories/FooterRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\FooterRepo.php", "line": 31}, {"index": 25, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 47}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 25}], "start": 1757381272.390852, "duration": 0.15753999999999999, "duration_str": "158ms", "memory": 0, "memory_str": null, "filename": "Url.php:68", "source": {"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FLibraries%2FUrl.php&line=68", "ajax": false, "filename": "Url.php", "line": "68"}, "connection": "wintoshop", "explain": null, "start_percent": 24.317, "width_percent": 5.117}, {"sql": "select `id` from `pages` where `pages`.`store_id` = 88 order by `id` desc", "type": "query", "params": [], "bindings": [88], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "beike/Repositories/PageRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\PageRepo.php", "line": 96}, {"index": 16, "namespace": null, "name": "beike/Repositories/PageRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\PageRepo.php", "line": 80}, {"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 123}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 170}, {"index": 24, "namespace": null, "name": "beike/Repositories/FooterRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\FooterRepo.php", "line": 31}], "start": 1757381272.549246, "duration": 0.20163999999999999, "duration_str": "202ms", "memory": 0, "memory_str": null, "filename": "PageRepo.php:96", "source": {"index": 15, "namespace": null, "name": "beike/Repositories/PageRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\PageRepo.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FRepositories%2FPageRepo.php&line=96", "ajax": false, "filename": "PageRepo.php", "line": "96"}, "connection": "wintoshop", "explain": null, "start_percent": 29.434, "width_percent": 6.549}, {"sql": "select * from `page_descriptions` where `locale` = 'zh_cn' and `page_descriptions`.`page_id` in (10, 11)", "type": "query", "params": [], "bindings": ["zh_cn"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "beike/Repositories/PageRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\PageRepo.php", "line": 96}, {"index": 21, "namespace": null, "name": "beike/Repositories/PageRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\PageRepo.php", "line": 80}, {"index": 22, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 123}, {"index": 23, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 170}, {"index": 29, "namespace": null, "name": "beike/Repositories/FooterRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\FooterRepo.php", "line": 31}], "start": 1757381272.753172, "duration": 0.15312, "duration_str": "153ms", "memory": 0, "memory_str": null, "filename": "PageRepo.php:96", "source": {"index": 20, "namespace": null, "name": "beike/Repositories/PageRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\PageRepo.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FRepositories%2FPageRepo.php&line=96", "ajax": false, "filename": "PageRepo.php", "line": "96"}, "connection": "wintoshop", "explain": null, "start_percent": 35.983, "width_percent": 4.973}, {"sql": "select * from `pages` where `active` = 1 and `pages`.`id` = 12 and `pages`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [1, 12, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 157}, {"index": 24, "namespace": null, "name": "beike/Repositories/FooterRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\FooterRepo.php", "line": 31}, {"index": 25, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 47}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 25}], "start": 1757381272.90728, "duration": 0.20352, "duration_str": "204ms", "memory": 0, "memory_str": null, "filename": "Url.php:68", "source": {"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FLibraries%2FUrl.php&line=68", "ajax": false, "filename": "Url.php", "line": "68"}, "connection": "wintoshop", "explain": null, "start_percent": 40.957, "width_percent": 6.61}, {"sql": "select * from `pages` where `active` = 1 and `pages`.`id` = 20 and `pages`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [1, 20, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 157}, {"index": 24, "namespace": null, "name": "beike/Repositories/FooterRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\FooterRepo.php", "line": 31}, {"index": 25, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 47}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 25}], "start": **********.1128561, "duration": 0.20097, "duration_str": "201ms", "memory": 0, "memory_str": null, "filename": "Url.php:68", "source": {"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FLibraries%2FUrl.php&line=68", "ajax": false, "filename": "Url.php", "line": "68"}, "connection": "wintoshop", "explain": null, "start_percent": 47.567, "width_percent": 6.528}, {"sql": "select * from `pages` where `active` = 1 and `pages`.`id` = 21 and `pages`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [1, 21, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 157}, {"index": 24, "namespace": null, "name": "beike/Repositories/FooterRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\FooterRepo.php", "line": 31}, {"index": 25, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 47}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 25}], "start": **********.314467, "duration": 0.15422999999999998, "duration_str": "154ms", "memory": 0, "memory_str": null, "filename": "Url.php:68", "source": {"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FLibraries%2FUrl.php&line=68", "ajax": false, "filename": "Url.php", "line": "68"}, "connection": "wintoshop", "explain": null, "start_percent": 54.095, "width_percent": 5.01}, {"sql": "select * from `pages` where `active` = 1 and `pages`.`id` = 11 and `pages`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [1, 11, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 157}, {"index": 21, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 48}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 25}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.470414, "duration": 0.25111, "duration_str": "251ms", "memory": 0, "memory_str": null, "filename": "Url.php:68", "source": {"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FLibraries%2FUrl.php&line=68", "ajax": false, "filename": "Url.php", "line": "68"}, "connection": "wintoshop", "explain": null, "start_percent": 59.105, "width_percent": 8.156}, {"sql": "select * from `pages` where `active` = 1 and `pages`.`id` = 10 and `pages`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [1, 10, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 157}, {"index": 21, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 48}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 25}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.722678, "duration": 0.20344, "duration_str": "203ms", "memory": 0, "memory_str": null, "filename": "Url.php:68", "source": {"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FLibraries%2FUrl.php&line=68", "ajax": false, "filename": "Url.php", "line": "68"}, "connection": "wintoshop", "explain": null, "start_percent": 67.261, "width_percent": 6.608}, {"sql": "select * from `customers` where `id` = 63 and `customers`.`deleted_at` is null and `customers`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [63, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 265}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.9329488, "duration": 0.21793, "duration_str": "218ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "wintoshop", "explain": null, "start_percent": 73.869, "width_percent": 7.079}, {"sql": "select * from `cart_products` where `session_id` = 'YkGz0mA2y0BMpk6lkQ8Dkf8G3sQ6bHa4Ejf6HE7d' and `cart_products`.`store_id` = 88 order by `id` desc", "type": "query", "params": [], "bindings": ["YkGz0mA2y0BMpk6lkQ8Dkf8G3sQ6bHa4Ejf6HE7d", 88], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "beike/Shop/Services/CartService.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Shop\\Services\\CartService.php", "line": 46}, {"index": 16, "namespace": null, "name": "beike/Shop/Http/Controllers/CartController.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Shop\\Http\\Controllers\\CartController.php", "line": 160}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.152756, "duration": 0.18281, "duration_str": "183ms", "memory": 0, "memory_str": null, "filename": "CartService.php:46", "source": {"index": 15, "namespace": null, "name": "beike/Shop/Services/CartService.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Shop\\Services\\CartService.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FShop%2FServices%2FCartService.php&line=46", "ajax": false, "filename": "CartService.php", "line": "46"}, "connection": "wintoshop", "explain": null, "start_percent": 80.947, "width_percent": 5.938}, {"sql": "select * from `customers` where `id` = 63 and `customers`.`deleted_at` is null and `customers`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [63, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 265}, {"index": 20, "namespace": null, "name": "beike/Shop/Http/Controllers/CartController.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Shop\\Http\\Controllers\\CartController.php", "line": 161}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.339864, "duration": 0.20066, "duration_str": "201ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "wintoshop", "explain": null, "start_percent": 86.885, "width_percent": 6.518}, {"sql": "select * from `currencies` where `status` = 1 and `currencies`.`store_id` = 88", "type": "query", "params": [], "bindings": [1, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "beike/Repositories/CurrencyRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\CurrencyRepo.php", "line": 91}, {"index": 16, "namespace": null, "name": "beike/Services/CurrencyService.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Services\\CurrencyService.php", "line": 25}, {"index": 17, "namespace": null, "name": "beike/Services/CurrencyService.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Services\\CurrencyService.php", "line": 33}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 358}, {"index": 20, "namespace": null, "name": "beike/Shop/Http/Controllers/CartController.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Shop\\Http\\Controllers\\CartController.php", "line": 161}], "start": **********.542253, "duration": 0.20312, "duration_str": "203ms", "memory": 0, "memory_str": null, "filename": "CurrencyRepo.php:91", "source": {"index": 15, "namespace": null, "name": "beike/Repositories/CurrencyRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\CurrencyRepo.php", "line": 91}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FRepositories%2FCurrencyRepo.php&line=91", "ajax": false, "filename": "CurrencyRepo.php", "line": "91"}, "connection": "wintoshop", "explain": null, "start_percent": 93.403, "width_percent": 6.597}]}, "models": {"data": {"Beike\\Models\\Language": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "Beike\\Models\\Page": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FModels%2FPage.php&line=1", "ajax": false, "filename": "Page.php", "line": "?"}}, "Beike\\Models\\Currency": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "Beike\\Models\\PageDescription": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FModels%2FPageDescription.php&line=1", "ajax": false, "filename": "PageDescription.php", "line": "?"}}}, "count": 13, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "O4jG04i8Bcl9bpHOIqVGqqnZWApkEJZpBCBit84f", "locale": "zh_cn", "login_web_shop_59ba36addc2b2f9401580f014c7f58ea4e30989d": "63", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://newshop.shopleade.test/carts/mini\"\n]", "url": "array:1 [\n  \"intended\" => \"http://jiuyia.shopleade.test/admin\"\n]", "histories": "array:6 [\n  0 => \"admin.theme.index\"\n  1 => \"admin.plugins.index\"\n  2 => \"admin.home.index\"\n  3 => \"admin.products.index\"\n  4 => \"admin.plugins.theme\"\n  5 => \"admin.settings.index\"\n]", "saas_front_locale": "zh_cn", "login_web_saas_user_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "page": "1", "login_web_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "74", "currency": "USD"}, "request": {"data": {"status": "200 OK", "full_url": "http://newshop.shopleade.test/carts/mini", "action_name": "shop.carts.mini", "controller_action": "Beike\\Shop\\Http\\Controllers\\CartController@miniCart", "uri": "GET carts/mini", "domain": "newshop.shopleade.test", "controller": "Beike\\Shop\\Http\\Controllers\\CartController@miniCart<a href=\"phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FShop%2FHttp%2FControllers%2FCartController.php&line=158\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/", "file": "<a href=\"phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FShop%2FHttp%2FControllers%2FCartController.php&line=158\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">beike/Shop/Http/Controllers/CartController.php:158-170</a>", "middleware": "shop", "duration": "5.23s", "peak_memory": "32MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-781703335 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-781703335\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-727470684 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-727470684\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-857187149 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"898 characters\">_ss_s_uid=df387fd27bfaa61c2967bdd458dc2053; beike_version={%22current%22:%*********%22%2C%22latest%22:%221.5.6%22%2C%22release_date%22:%222025-05-08%22%2C%22has_new_version%22:true}; XSRF-TOKEN=eyJpdiI6IldmcTVMQnJtb2hBcnQzMGcySkw2UVE9PSIsInZhbHVlIjoidmVmNmYyMFYyRGpuWkwvUFdxZjNydTlvTVk3OURFdXVzNmN0K0doN3Q2M21jK2YrSEE3REUreEdCTFVSNE9xRlJZNWI3eElCL3dzaFEzMU9EYnN1bTVqamt1eHpQN3lvYm15N3F2ZC9OQ0MxaUh0b0pUS2FpNURra1lEUHpwc3MiLCJtYWMiOiJjZDZiMDUwM2RhNDM0ZDQ4ZGU1MTdhNTgyYzg0MjVkY2MxYzVmMzU5YmQ5MDAxODJjN2M4MTRjYTQzZTVlNjIxIiwidGFnIjoiIn0%3D; shopleade_session=eyJpdiI6ImdCb1M2eXZJM3lncDB6Z0lkRUI0Zmc9PSIsInZhbHVlIjoiTTZnOVRQK2hHaWx3ZVBjdDd6OUc2ZXNNWGg1OUVIUnBWUEdJZ29DbmJCTXVRd3VxSmxRamZ0cVFkYWRsM040R0VYYUVBejRhNTRJcDR0R2lyR3MxNkk5RFZGWnpENVRGeGdudmNkd1kzdit6bVJoNW84WmlJRlp5Wml4VHZ1Y0QiLCJtYWMiOiIwZTQ4ODNiZGZhY2Y5ODU2MGQyMDA2OWE3Yzk0OGRlODhiYjJjZGNjNTQ3ZDI3MTUxNDJjMmQ0Yjk3YTg5YTI2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">zh-CN,zh;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"45 characters\">http://newshop.shopleade.test/latest_products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IldmcTVMQnJtb2hBcnQzMGcySkw2UVE9PSIsInZhbHVlIjoidmVmNmYyMFYyRGpuWkwvUFdxZjNydTlvTVk3OURFdXVzNmN0K0doN3Q2M21jK2YrSEE3REUreEdCTFVSNE9xRlJZNWI3eElCL3dzaFEzMU9EYnN1bTVqamt1eHpQN3lvYm15N3F2ZC9OQ0MxaUh0b0pUS2FpNURra1lEUHpwc3MiLCJtYWMiOiJjZDZiMDUwM2RhNDM0ZDQ4ZGU1MTdhNTgyYzg0MjVkY2MxYzVmMzU5YmQ5MDAxODJjN2M4MTRjYTQzZTVlNjIxIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">newshop.shopleade.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-857187149\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-233836533 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_ss_s_uid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>beike_version</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">O4jG04i8Bcl9bpHOIqVGqqnZWApkEJZpBCBit84f</span>\"\n  \"<span class=sf-dump-key>shopleade_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YkGz0mA2y0BMpk6lkQ8Dkf8G3sQ6bHa4Ejf6HE7d</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-233836533\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1938647677 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 09 Sep 2025 01:27:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"455 characters\">XSRF-TOKEN=eyJpdiI6Im93Tnk4N1YyRHl6NHJ3SUV0KzIwT0E9PSIsInZhbHVlIjoiT0VScysrSEMrdTBwYjI0MVd0OUNSWVlWTlViamN1YlNyNTZSV0hWbThJS21DbElsaVgxZ1AxelVSNDh1eTlZT2tOcmoyVVRiMmx5bit5R1dpdXg1eW5COGlWTzQ5UDVXT0JyU3E4SHYxNFRRbXVIS3kyS2NTL1FoeHcwc2wxWVciLCJtYWMiOiIzZjNkYWY1NzE1ZGMyMWExYzI3MmRmNzA0YmMwZGJkZWEzMzJjMmQ3N2I0MDJhNmI5MmI1OGM4OTZlYjIzNTdjIiwidGFnIjoiIn0%3D; expires=Thu, 09 Oct 2025 01:27:54 GMT; Max-Age=2592000; path=/; domain=.shopleade.test; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"472 characters\">shopleade_session=eyJpdiI6IkM0SURGSmlGOVJYeGhWZDNwWFFNZEE9PSIsInZhbHVlIjoiU3VycTdaVDczYWJuQmdRMkFQSHRDL0Z0MGJNZXBqdTBNbWJHTEJIOTBqY0ZWMWxKcnpya0FrY0NxTit6SzZKeklMUlRnY0tlQzdGdHhKUGJpTlMrL2xpVHNXZ2NPSWdIV3pxRVFhWVRucWJSNFRwOEh0TnVaaFE1TWNiZjh6Z2IiLCJtYWMiOiJiNDNjZTdjNmE1ZjE1Zjk1MTFkZWYyOTA0ZmZjYWE2ZWJlMmQ3MGIwYjM4MTA0ZTQzNTNhYjk2NmUzYTY2MmRiIiwidGFnIjoiIn0%3D; expires=Thu, 09 Oct 2025 01:27:54 GMT; Max-Age=2592000; path=/; domain=.shopleade.test; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"424 characters\">XSRF-TOKEN=eyJpdiI6Im93Tnk4N1YyRHl6NHJ3SUV0KzIwT0E9PSIsInZhbHVlIjoiT0VScysrSEMrdTBwYjI0MVd0OUNSWVlWTlViamN1YlNyNTZSV0hWbThJS21DbElsaVgxZ1AxelVSNDh1eTlZT2tOcmoyVVRiMmx5bit5R1dpdXg1eW5COGlWTzQ5UDVXT0JyU3E4SHYxNFRRbXVIS3kyS2NTL1FoeHcwc2wxWVciLCJtYWMiOiIzZjNkYWY1NzE1ZGMyMWExYzI3MmRmNzA0YmMwZGJkZWEzMzJjMmQ3N2I0MDJhNmI5MmI1OGM4OTZlYjIzNTdjIiwidGFnIjoiIn0%3D; expires=Thu, 09-Oct-2025 01:27:54 GMT; domain=.shopleade.test; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">shopleade_session=eyJpdiI6IkM0SURGSmlGOVJYeGhWZDNwWFFNZEE9PSIsInZhbHVlIjoiU3VycTdaVDczYWJuQmdRMkFQSHRDL0Z0MGJNZXBqdTBNbWJHTEJIOTBqY0ZWMWxKcnpya0FrY0NxTit6SzZKeklMUlRnY0tlQzdGdHhKUGJpTlMrL2xpVHNXZ2NPSWdIV3pxRVFhWVRucWJSNFRwOEh0TnVaaFE1TWNiZjh6Z2IiLCJtYWMiOiJiNDNjZTdjNmE1ZjE1Zjk1MTFkZWYyOTA0ZmZjYWE2ZWJlMmQ3MGIwYjM4MTA0ZTQzNTNhYjk2NmUzYTY2MmRiIiwidGFnIjoiIn0%3D; expires=Thu, 09-Oct-2025 01:27:54 GMT; domain=.shopleade.test; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1938647677\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-285862943 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">O4jG04i8Bcl9bpHOIqVGqqnZWApkEJZpBCBit84f</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"5 characters\">zh_cn</span>\"\n  \"<span class=sf-dump-key>login_web_shop_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>63</span>\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://newshop.shopleade.test/carts/mini</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://jiuyia.shopleade.test/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>histories</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">admin.theme.index</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"19 characters\">admin.plugins.index</span>\"\n    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">admin.home.index</span>\"\n    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"20 characters\">admin.products.index</span>\"\n    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"19 characters\">admin.plugins.theme</span>\"\n    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"20 characters\">admin.settings.index</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>saas_front_locale</span>\" => \"<span class=sf-dump-str title=\"5 characters\">zh_cn</span>\"\n  \"<span class=sf-dump-key>login_web_saas_user_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>login_web_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">USD</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-285862943\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://newshop.shopleade.test/carts/mini", "action_name": "shop.carts.mini", "controller_action": "Beike\\Shop\\Http\\Controllers\\CartController@miniCart"}, "badge": null}}