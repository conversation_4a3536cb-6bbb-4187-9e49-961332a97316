

<?php $__env->startSection('body-class', 'page-login'); ?>

<?php $__env->startPush('header'); ?>
    <script src="<?php echo e(asset('vendor/vue/3/vue.global' . (!config('app.debug') ? '.prod' : '') . '.js')); ?>"></script>
    <script src="<?php echo e(asset('vendor/vue/3x/vue.global' . (!config('app.debug') ? '.prod' : '') . '.js')); ?>"></script>
     <script type="module" src="<?php echo e(asset('vendor/utils/seturlparams.js')); ?>"></script>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
    <div class="login-container" id="App" v-cloak>

        <div class="login-cont flex">

            <div class="login-lf-scroll" v-if="!changesuccess">
                <div class="login-lf relative">
                    <div class="lang-logo-cont flex flex-center line-center relative">
                        <div class="flex flex-center line-center">
                            <span>SHOPLEADE</span>
                        </div>
                    </div>

                    <div class="lang-cont absolute">
                        <i class="login_iconfont icon-duoyuyan"></i> {{ languageData.filter(item => item.code === activeLangueData)[0]?.name }}
                        <div class="lang-item-cont move">
                            <div class="lang-item" v-for="item in languageData">
                                <a style="" :href="item.link">{{ item.name }}</a>
                            </div>
                        </div>
                    </div>

                     <div class="home-cont absolute cursor">
                        <a href="/"><i class="login_iconfont icon-shouye"></i></a>
                    </div>

                    <div v-if='loadingKey' class="loading-cont flex flex-center line-center">
                        <i class="login_iconfont icon-loading1"></i>
                    </div>
                    <div class="login-text relative">
                        <span> {{ formInfo.title }}</span>
                        <div class="absolute create_btn" v-if="activeForm.name === 'create'">
                            <span><?php echo e(__('saas_front::login.yiyouzhanghao')); ?></span><span
                                @click="setActiveForm(0)"
                                class="cursor"><?php echo e(__('saas_front::login.dianjidenglu')); ?></span>
                        </div>
                    </div>
                    <form @submit.prevent="submitForm" class="form-cont" method="post" novalidate>
                        <?php echo csrf_field(); ?>
                        <div class="form-cont">
                            
                            <div class="juhe-create" v-if="activeForm.name === 'juhe_create'">
                                <div class="juhe-create-title">
                                    <?php echo e(__('saas_front::login.chuangjianzhanghao')); ?>

                                </div>
                                <div class="juhe-create-des"><?php echo e(__('saas_front::login.jiangshiyonogninde')); ?>

                                    {{ activeForm.data[0].createType }} <?php echo e(__('saas_front::login.zhuceSHOPLEADEpingtai')); ?></div>
                                <div class="juhe-create-crrent"><?php echo e(__('saas_front::login.dangqian')); ?>

                                    {{ JU_HE_DATA[urlData['third_channel_name']] }}
                                    <?php echo e(__('saas_front::login.zhanghu')); ?></div>
                                <div class="juhe-create-email">{{ urlData?.email }}</div>
                            </div>
                            
                            <div class="input-item"
                                :class="{
                                    create: activeForm.name === 'create' && item.type === 'checkbox',
                                }"
                                v-for="item,i in activeForm.data" :key="i">
                                <div class="input-cont relative"
                                    :class='{
                                        itemText: true,
                                        error: !item.verify,
                                        itemPassword: item.key[0] === "password" || item
                                            .key[0] === "twoPassword"
                                    }'
                                    v-if="item.type === 'text'">
                                    <input @input="changeValue(item)" :name='item.key[0]'
                                        v-model="item.value" type="text" :placeholder="item.placeholder">

                                    <div data-bs-toggle="dropdown"
                                        class="form-select flex line-center flex-center calling-code-select"
                                        v-if="accountIsNumber && item.key[0] === 'email'">
                                        <img :src="'<?php echo e(asset('image/flag')); ?>/' + selectedCallingCode.icon + '.png'"
                                            class="img-fluid" style="width: 16px">
                                        <span>+ {{ selectedCallingCode.code }}</span>
                                    </div>

                                    <div class="dropdown-menu calling-code-dropdown"
                                        v-if="accountIsNumber && item.key[0] === 'email'">
                                        <div class="position-relative">
                                            <i class="iconfont position-absolute top-0 start-0 ms-2"
                                                style="margin-top: 6px">&#xe65b;</i>
                                            <input type="text" placeholder="Search your country and region"
                                                name="" class="form-control ps-4" id="calling-code-search">
                                        </div>
                                        <ul class="code-list">
                                            <li v-for="item in source.callingCodes"
                                                @click='city = item.code'
                                                :data-text="item.region + ' ' + item.code">
                                                <span>
                                                    <img :src="'<?php echo e(asset('image/flag')); ?>/' + item.icon + '.png'"
                                                        class="img-fluid">
                                                    {{ item.region }}
                                                </span>
                                                <span>{{ item.code }}</span>
                                            </li>
                                        </ul>
                                    </div>

                                    <span @click="item.showOrHidden ? item.showOrHidden(item) : ''"
                                        v-if="item.iconSrc" class="absolute texticon cursor">
                                        <i class="login_iconfont" :class="item.iconSrc"></i>
                                    </span>
                                    <span class="error-text  overflow1 absolute"
                                        :title="item.verifyText">{{ item.verifyText }}</span>
                                </div>
                                <div class="input-cont relative" :class='{ itemPassword: true, error: !item.verify }'
                                    v-else-if="item.type === 'password'">
                                    <input @input="changeValue(item)" :name='item.key[0]'
                                        new-password current-password v-model="item.value" type="password"
                                        :placeholder="item.placeholder">
                                    <span v-if="item.iconSrc" class="absolute passicon cursor"
                                        @click="item.showOrHidden(item)">
                                        <i class="login_iconfont" :class="item.iconSrc"></i>
                                    </span>
                                    <span class="error-text overflow1 absolute"
                                        :title="item.verifyText">{{ item.verifyText }}</span>
                                </div>
                                <div class="input-cont relative time-text" :class='{ error: !item.verify }'
                                    v-else-if="item.type === 'text-time'">
                                    <input @input="item.verify = true" :name='item.key[0]'
                                        v-model="item.value" type="text" :placeholder="item.placeholder">
                                    <span class="absolute passicon" :class={cursor:!item.isCounting}
                                        @click="getCode(item,subFn,activeForm.name)">
                                        {{ item.timeText }}
                                    </span>
                                    <span class="error-text overflow1 absolute"
                                        :title="item.verifyText">{{ item.verifyText }}</span>
                                </div>
                                <div class="input-cont" v-else-if="item.type === 'checkbox'">
                                    <div class="flex flex-warp line-center" :class='{ itemCheckout: true }'>
                                        <label for="xieyi" class="cursor">
                                            <input v-model="item.value" type="checkbox" id="xieyi">
                                            <span class="check-span">
                                            </span>
                                        </label>
                                        <span><?php echo e(__('saas_front::login.zhengche')); ?></span>
                                        <a href="/webpages/user_agreement"
                                            target="_blank"><?php echo e(__('saas_front::login.Agreement')); ?></a>,
                                        <a href="/webpages/privacy_policy"
                                            target="_blank"><?php echo e(__('saas_front::login.xieyi')); ?></a>
                                    </div>
                                </div>

                            </div>
                            <div v-if="activeForm.name === 'login'" class="user-selset flex line-center between">
                                <span @click="setActiveForm(3)"
                                    class="cursor"><?php echo e(__('saas_front::login.chuangjian_zhanghao')); ?></span>
                                <span @click="setActiveForm(1)"
                                    class="cursor"><?php echo e(__('saas_front::login.wangji_mima')); ?></span>
                            </div>
                            
                            <div class="juhe_login" v-if="activeForm.name === 'login' || activeForm.name === 'create'">
                                <div class="or relative">
                                    <span><?php echo e(__('saas_front::login.huozhe')); ?></span>
                                </div>
                                <div class="cursor juhe_item flex line-center flex-center">
                                    <a class="flex line-center flex-center"
                                        :href="`/auth/google${objectToQueryParams(urlData) ? objectToQueryParams(urlData) : ''}`"><img
                                            src="/image/icon/google.svg"><?php echo e(__('saas_front::login.google')); ?></a>
                                </div>
                                <div class="cursor juhe_item flex line-center flex-center">
                                    <a class="flex line-center flex-center"
                                        :href="`/facebook/redirect${objectToQueryParams(urlData) ? objectToQueryParams(urlData) : ''}`"><img
                                            src="/image/icon/facebook.svg"><?php echo e(__('saas_front::login.facebook')); ?></a>
                                </div>
                                
                            </div>
                            <div class="submit-btn move"
                                :class="{ active: !submitActive,login:activeForm.name==='login' }">
                                <button type="submit">{{ formInfo.buttonText }}</button>
                            </div>
                            <div class="black-btn flex flex-center cursor" @click="setActiveForm(0)"
                                v-if="activeForm.name === 'forgot_password' || activeForm.name === 'reset_password' || activeForm.name === 'juhe_create'">
                                <span><?php echo e(__('saas_front::login.fanhui')); ?></span>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="login-lf-scroll" v-else>
                <div class="login-lf  relavtive change-success flex line-center">
                    <div v-if='loadingKey' class="loading-cont flex flex-center line-center">
                        <i class="login_iconfont icon-loading1"></i>
                    </div>
                    <div class="flex flex-warp" v-if="changesuccess == 'mimasuccess'">
                        <div class="chang-success-title">
                            <?php echo e(__('saas_front::login.chongzhi_mima')); ?>

                        </div>
                        <p class="chang-suuccess-des">{{ code_form_data.email ? code_form_data.email : code_form_data.telephone }}<?php echo e(__('saas_front::login.chongzhi_mima')); ?>

                        </p>
                        <p class="chang-sucucess-tishi"> <?php echo e(__('saas_front::login.chongzhimima')); ?></p>
                        <p class="change-success-btn cursor" @click="setActiveForm(0)">
                            <?php echo e(__('saas_front::login.fanhuidenglu')); ?></p>
                    </div>
                    <div class="flex flex-warp" v-else-if='changesuccess == "zhucesuccess"'>
                        <div class="chang-success-title">
                            <?php echo e(__('saas_front::login.register_text')); ?>

                        </div>
                        <p class="chang-suuccess-des">
                            {{ code_form_data.email ? code_form_data.email : code_form_data.telephone }}<?php echo e(__('saas_front::login.chuangjian_zhanghao')); ?>

                        </p>
                        <p class="chang-sucucess-tishi">
                            <?php echo e(__('saas_front::login.zhuchechenggong')); ?><?php echo e(__('saas_front::login.jijiangzhidongdenglu')); ?>

                            {{ aoutlogin }}</p>
                        <p class="change-success-btn cursor"
                            @click="toUserHome('/register/question')">
                            <?php echo e(__('saas_front::login.dianjilijidenglu')); ?></p>
                    </div>
                    <div class="flex flex-warp" v-else-if='changesuccess == "juhesuccess"'>
                        <div v-if="!juhe_Data">
                            <div class="chang-success-title">
                                <?php echo e(__('saas_front::login.shouquanchenggong')); ?>

                            </div>
                            <p class="chang-suuccess-des">
                                <?php echo e(__('saas_front::login.yijingshouquanchenggong')); ?>

                                {{ JU_HE_DATA[urlData['third_channel_name']] }} ,
                                <?php echo e(__('saas_front::login.zhanghao_jijiangzidongdenglu')); ?>

                            </p>
                        </div>
                        <div v-else>
                            <div class="chang-success-title">
                                <?php echo e(__('saas_front::login.denglushibai')); ?>

                            </div>
                            <p class="change-success-des">
                                {{ JU_HE_DATA[urlData['third_channel_name']] }}
                                <?php echo e(__('saas_front::login.denglushibai_textt')); ?>

                                <span class="cursor" @click="(juhe_Data = false) || setActiveForm(0)"
                                    style="color: var(--btnColor)"> <?php echo e(__('saas_front::login.fanhuidenglu')); ?></span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="login-bg">
                <div class="flex flex-center line-center ">
                    <a class="cursor" style="color: white" href="/"><span>SHOPLEADE</span></a>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('footer'); ?>
    <script type="module">
        import { objectToQueryParams } from '/vendor/utils/seturlparams.js';
        const csrfToken = document.head.querySelector('meta[name="csrf-token"]').content;
        function getUrlParams() {
            const searchParams = new URLSearchParams(window.location.search);
            const params = {};
            for (const [key, value] of searchParams.entries()) {
                params[key] = value;
            }
            return params;
        };
        const myaxios = axios.create({
            headers: {
                'X-CSRF-TOKEN': csrfToken
            }
        });
        const {
            ref,
            reactive,
            createApp,
            computed,
            watch,
            nextTick,
            onMounted
        } = Vue3;
        const icons_data = {
            hidden: `icon-yincang1`,
            show: `icon-xianshi1 `,
            success: `icon-dagou1`,
            error: `icon-chacha`,
        }
        myaxios.interceptors.request.use(config => {
            config.headers['X-CSRF-TOKEN'] = document.head.querySelector('meta[name="csrf-token"]').content;
            if (config.method === 'get') {
                config.url.indexOf('&') == -1 ? config.url += `?_token=${csrfToken}` : config.url +=
                    `&_token=${csrfToken}`
            }
            if (config.method === 'post') {
                config.data || (config.data = {});
                config.data._token = csrfToken;
            }
            return config;
        });
        myaxios.interceptors.response.use(config => {
            return config.data;
        }, config => {
            if (config.status === 419) {
                return {
                    code: config.status,
                    ...config.response.data,
                }
            }
            return config.response.data
        });
        const App = createApp({
            setup() {
                const loginLf = ref();
                let code_form_data = ref({});
                let isgetCode = true;
                const loadingKey = ref(false);
                let aoutlogin = ref(5);
                const urlData = getUrlParams();
                const JU_HE_DATA = {
                    google: `Google`,
                    facebook: `Facebook`,
                    apple: `Apple`
                };
                const juhe_Data = ref(false);
                const activeLangueData = <?php echo json_encode(@saas_front_locale(), 15, 512) ?>;
                const language = <?php echo json_encode(collect(saas_locales())->map(function ($item) {
                        return array_merge((array) $item, [
                            'link' => saas_front_route('lang.switch', $item['code'])
                        ]);
                    })) ?>;
                const languageData = ref(JSON.parse(JSON.stringify(language)));
                const juhe_invite_code = ref(urlData.invite_code ? urlData.invite_code : '');
                let userAgreement = ``;
                //显示或隐藏图标
                function showOrHidden(obj) {
                    if (obj.key[0] === 'password' || obj.key[0] === 'twoPassword') {
                        if (obj.type === 'password') {
                            obj.type = 'text';
                            obj.iconSrc = icons_data.show;
                        } else {
                            obj.type = 'password';
                            obj.iconSrc = icons_data.hidden;
                        }
                    }
                };

                function showMsgInElement(msg, callback) {
                    const typeString = typeof msg;
                    const typeEvent = {
                        'string'() {
                            layer.msg(msg, {
                                shade: false,
                                time: 3000
                            });
                        },
                        'object'() {
                            if (msg.config) {
                                layer.open({
                                    type: 1, // 自定义类型
                                    title: msg.showText,
                                    content: msg.config.content,
                                    area: msg.config.area,
                                    btn: [`${msg.btnText}(${msg.config.time}s)`],
                                    btnAlign: 'c',
                                    closeBtn: 0,
                                    yes: callback,
                                    success: function(layero, index) {
                                        // 弹层创建成功后回调
                                        const btn = layero.find('.layui-layer-btn').children().eq(
                                            0);
                                        if (window.screen.width > 768) {
                                            layero.find('.page-user-agreement').css(
                                                'padding', '0 50px 5vh 50px')
                                        } else {
                                            layero.find('.page-user-agreement').css(
                                                'padding', '0 20px 5vh 20px')
                                        }

                                        btn.css('cursor', 'not-allowed').attr('disabled',
                                            'disabled');
                                        btn.css('pointer-events', 'none');
                                        btn.css('height', 'fit-content');
                                        btn.css('width', '100%');
                                        // 倒计时定时器
                                        msg.config.timer = setInterval(function() {
                                            msg.config.time--;
                                            btn.text(
                                                `${msg.btnText}(${msg.config.time}s)`);
                                            if (msg.config.time <= 0) {
                                                clearInterval(msg.config.timer);
                                                btn.text(`${msg.btnText}`)
                                                    .css('pointer-events', 'inherit')
                                                    .css('cursor', 'pointer')
                                                    .removeAttr('disabled');
                                            }
                                        }, 1000);
                                    },
                                    cancel: function() {
                                        // 防止用户通过其他方式关闭
                                        return false;
                                    }
                                });
                            } else {
                                layer.msg(msg.showText, {
                                    time: 0,
                                    btn: [msg.buttonText],
                                    btnAlign: 'c',
                                    yes: function(index) {
                                        layer.close(index);
                                        callback && callback();
                                    }
                                });
                            }
                        }
                    }
                    typeEvent[typeString]();
                };
                //修改字段文本
                function setText(obj, key, text) {
                    obj[key] = text;
                    return true;
                };

                function forVerifyFn(fnData) {
                    for (let i = 0; i < fnData.length; i++) {
                        if (!fnData[i]()) {
                            break;
                        }
                    }
                };
                //验证规则方法集合
                function verifyFnCont() {
                    //长度验证
                    function verifyLength(obj, text) {
                        if (obj.value.length > 0) {
                            obj.verify = true
                            return true;
                        };
                        obj.verify = false;
                        (text) && (setText(obj, `verifyText`, text));
                        return false;
                    };

                    //邮箱或者手机号验证
                    function isEmailorPhone(obj, text, type = true) {
                        if (type) {
                            if (isEmail(obj.value) || isNum(obj.value)) {
                                obj.verify = true;
                                return true;
                            }
                            obj.verify = false;
                            setText(obj, `verifyText`, text);
                            return false;
                        } else {
                            return isEmail(obj.value) || isNum(obj.value)
                        }
                    };
                    //邮箱验证
                    function isEmail(value) {
                        const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
                        return emailRegex.test(value)
                    }
                    //纯数字验证
                    function isNum(value) {
                        const phoneRegex = /^\d+$/;
                        return phoneRegex.test(value)
                    }
                    //密码强度验证
                    function passwordVerify(obj, text) {
                        const regex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*[^a-zA-Z0-9]).{8,}$/;
                        if (regex.test(obj.value)) {
                            obj.verify = true;
                            return true;
                        };

                        obj.verify = false;
                        setText(obj, `verifyText`, text);
                        return false;
                    };

                    //密码是否一致
                    function passwordIs(obj1, obj2, text) {
                        if (obj1.value === obj2.value) {
                            (obj1.verify = true) && (obj2.verify = true)
                            return true;
                        };
                        (obj1.verify = false) && (obj2.verify = false);
                        (setText(obj1, `verifyText`, text)) && (setText(obj2, `verifyText`, text));
                        return false;
                    };
                    return {
                        verifyLength,
                        passwordVerify,
                        passwordIs,
                        isEmailorPhone,
                        isEmail,
                        isNum
                    }
                };
                const {
                    verifyLength,
                    passwordVerify,
                    passwordIs,
                    isEmailorPhone,
                    isEmail,
                    isNum
                } = verifyFnCont();
                //表单数据
                function getFormData() {
                    //国家区号
                    const city = ref(<?php echo json_encode(old('calling_code', $calling_codes[0]['code'] ?? ''), 512) ?>);
                    //表单配置
                    const formData = reactive({
                        xieyi: false,
                        buttonActive: false,
                        formList: [{
                                name: `login`,
                                active: false,
                                data: [{
                                        type: 'text',
                                        value: ``,
                                        placeholder: <?php echo json_encode(__('saas_front::login.user_number'), 15, 512) ?>,
                                        verify: true,
                                        verifyText: <?php echo json_encode(__('saas_front::login.login_verify_text'), 15, 512) ?>,
                                        key: [
                                            `email`,
                                            `account`
                                        ],
                                        iconSrc: icons_data.error,
                                    },
                                    {
                                        type: 'password',
                                        value: ``,
                                        placeholder: <?php echo json_encode(__('saas_front::login.user_password'), 15, 512) ?>,
                                        verify: true,
                                        verifyText: <?php echo json_encode(__('saas_front::login.login_verify_password'), 15, 512) ?>,
                                        iconSrc: icons_data.hidden,
                                        showOrHidden,
                                        key: [
                                            `password`
                                        ]
                                    },
                                ]
                            },
                            {
                                name: `forgot_password`,
                                active: false,
                                data: [{
                                        type: 'text',
                                        value: ``,
                                        placeholder: <?php echo json_encode(__('saas_front::login.user_number'), 15, 512) ?>,
                                        verify: true,
                                        verifyText: <?php echo json_encode(__('saas_front::login.login_verify_text'), 15, 512) ?>,
                                        key: [
                                            `email`,
                                            `account`
                                        ],
                                        verifyCallBack() {
                                            const fnData = [
                                                () => isEmailorPhone(this,
                                                    <?php echo json_encode(__('saas_front::login.wangji_mima_yanzheng'), 15, 512) ?>),
                                            ]
                                            forVerifyFn(fnData);
                                        },
                                    },
                                    {
                                        codeType: `wangjimima`,
                                        type: 'text-time',
                                        value: ``,
                                        placeholder: <?php echo json_encode(__('saas_front::login.yanzhengma'), 15, 512) ?>,
                                        verify: true,
                                        verifyText: <?php echo json_encode(__('saas_front::login.wangji_mima_yanzheng'), 15, 512) ?>,
                                        key: [
                                            `verify_code`
                                        ],
                                        isCounting: false,
                                        timeNum: 30,
                                        timeText: <?php echo json_encode(__('saas_front::login.huoqu_yanzhengma'), 15, 512) ?>
                                    }
                                ]
                            },
                            {
                                name: 'reset_password',
                                active: false,
                                data: [{
                                        type: 'password',
                                        value: ``,
                                        placeholder: <?php echo json_encode(__('saas_front::login.user_password'), 15, 512) ?>,
                                        verify: true,
                                        verifyText: <?php echo json_encode(__('saas_front::login.login_verify_password'), 15, 512) ?>,
                                        verifyCallBack() {
                                            const fnData = [
                                                () => passwordVerify(this,
                                                    <?php echo json_encode(__('saas_front::login.password_error'), 15, 512) ?>),
                                                () => passwordIs(this, formData.formList[2]
                                                    .data[1],
                                                    <?php echo json_encode(__('saas_front::login.password_no_one'), 15, 512) ?>),

                                            ]
                                            forVerifyFn(fnData);

                                        },
                                        iconSrc: icons_data.hidden,
                                        showOrHidden,
                                        key: [
                                            `password`
                                        ]
                                    },
                                    {
                                        type: 'password',
                                        value: ``,
                                        placeholder: <?php echo json_encode(__('saas_front::login.user_password'), 15, 512) ?>,
                                        verify: true,
                                        verifyText: <?php echo json_encode(__('saas_front::login.login_verify_password'), 15, 512) ?>,
                                        verifyCallBack() {
                                            const fnData = [
                                                () => passwordVerify(this,
                                                    <?php echo json_encode(__('saas_front::login.password_error'), 15, 512) ?>),
                                                () => passwordIs(this, formData.formList[2]
                                                    .data[0],
                                                    <?php echo json_encode(__('saas_front::login.password_no_one'), 15, 512) ?>),

                                            ]
                                            forVerifyFn(fnData);

                                        },

                                        iconSrc: icons_data.hidden,
                                        showOrHidden,
                                        key: [
                                            `twoPassword`
                                        ]
                                    }
                                ]
                            },
                            {
                                name: `create`,
                                active: false,
                                data: [{
                                        type: 'text',
                                        value: ``,
                                        placeholder: <?php echo json_encode(__('saas_front::login.user_number'), 15, 512) ?>,
                                        verify: true,
                                        verifyText: <?php echo json_encode(__('saas_front::login.login_verify_text'), 15, 512) ?>,
                                        key: [
                                            `email`,
                                            `account`
                                        ],
                                        verifyCallBack() {
                                            const fnData = [
                                                () => isEmailorPhone(this,
                                                    <?php echo json_encode(__('saas_front::login.wangji_mima_yanzheng'), 15, 512) ?>),
                                            ]
                                            forVerifyFn(fnData);
                                        },
                                    },
                                    {
                                        codeType: `zhuce`,
                                        type: 'text-time',
                                        value: ``,
                                        placeholder: <?php echo json_encode(__('saas_front::login.yanzhengma'), 15, 512) ?>,
                                        verify: true,
                                        verifyText: <?php echo json_encode(__('saas_front::login.yanzhengma_cuowu'), 15, 512) ?>,
                                        key: [
                                            `verify_code`,
                                        ],
                                        isCounting: false,
                                        timeNum: 30,
                                        timeText: <?php echo json_encode(__('saas_front::login.huoqu_yanzhengma'), 15, 512) ?>
                                    },
                                    {
                                        type: 'password',
                                        value: ``,
                                        placeholder: <?php echo json_encode(__('saas_front::login.user_password'), 15, 512) ?>,
                                        verify: true,
                                        verifyText: <?php echo json_encode(__('saas_front::login.login_verify_password'), 15, 512) ?>,
                                        verifyCallBack() {
                                            const fnData = [
                                                () => passwordVerify(this,
                                                    <?php echo json_encode(__('saas_front::login.password_error'), 15, 512) ?>),
                                                () => passwordIs(this, formData.formList[3]
                                                    .data[3],
                                                    <?php echo json_encode(__('saas_front::login.password_no_one'), 15, 512) ?>)
                                            ]
                                            forVerifyFn(fnData);
                                        },
                                        iconSrc: icons_data.hidden,
                                        showOrHidden,
                                        key: [
                                            `password`
                                        ]
                                    },
                                    {
                                        type: 'password',
                                        value: ``,
                                        placeholder: <?php echo json_encode(__('saas_front::login.user_comfirm_password'), 15, 512) ?>,
                                        verify: true,
                                        verifyText: <?php echo json_encode(__('saas_front::login.login_verify_password'), 15, 512) ?>,
                                        verifyCallBack() {
                                            const fnData = [
                                                () => passwordVerify(this,
                                                    <?php echo json_encode(__('saas_front::login.password_error'), 15, 512) ?>),
                                                () => passwordIs(this, formData.formList[3]
                                                    .data[2],
                                                    <?php echo json_encode(__('saas_front::login.password_no_one'), 15, 512) ?>)
                                            ]
                                            forVerifyFn(fnData);
                                        },
                                        iconSrc: icons_data.hidden,
                                        key: [
                                            `password`
                                        ],
                                        showOrHidden
                                    },
                                    {
                                        name: `invite_code`,
                                        type: 'text',
                                        value: urlData?.invite_code ? urlData?.invite_code : ``,
                                        placeholder: <?php echo json_encode(__('saas_front::login.yaoqingma'), 15, 512) ?>,
                                        verify: true,
                                        required: false,
                                        key: [
                                            `invite_code`
                                        ],
                                    },
                                    {
                                        type: 'checkbox',
                                        verify: true,
                                    },

                                ]
                            },
                            {
                                name: `juhe_create`,
                                active: false,
                                data: [{
                                    type: 'checkbox',
                                    value: false,
                                    verify: true,
                                    createType: urlData?.third_channel_name
                                }, ]
                            },
                            {
                                name: `facebook_create`,
                                active: false,
                                data: [{
                                    type: 'text',
                                    value: ``,
                                    placeholder: <?php echo json_encode(__('saas_front::login.user_number'), 15, 512) ?>,
                                    verify: true,
                                    verifyText: <?php echo json_encode(__('saas_front::login.login_verify_text'), 15, 512) ?>,
                                    key: [
                                        `email`,
                                        `account`
                                    ],
                                    verifyCallBack() {
                                        const fnData = [
                                            () => isEmailorPhone(this,
                                                <?php echo json_encode(__('saas_front::login.wangji_mima_yanzheng'), 15, 512) ?>),
                                        ]
                                        forVerifyFn(fnData);
                                    },
                                }, ]
                            }
                        ]
                    });
                    //表单标题
                    const titleList = {
                        login: <?php echo json_encode(__('saas_front::login.denglu_huanying'), 15, 512) ?>,
                        forgot_password: <?php echo json_encode(__('saas_front::login.wangji_mima'), 15, 512) ?>,
                        reset_password: <?php echo json_encode(__('saas_front::login.chongzhi_mima'), 15, 512) ?>,
                        create: <?php echo json_encode(__('saas_front::login.chuangjian_zhanghao'), 15, 512) ?>,
                    }
                    //提交按钮文字
                    const buttonText = {
                        login: <?php echo json_encode(__('saas_front::login.denglu'), 15, 512) ?>,
                        forgot_password: <?php echo json_encode(__('saas_front::login.yanzheng'), 15, 512) ?>,
                        reset_password: <?php echo json_encode(__('saas_front::login.comfirm_password'), 15, 512) ?>,
                        create: <?php echo json_encode(__('saas_front::login.register'), 15, 512) ?>,
                        juhe_create: <?php echo json_encode(__('saas_front::login.chuangjianzhanghao'), 15, 512) ?>,
                        facebook_create: <?php echo json_encode(__('saas_front::login.chuangjianzhanghao'), 15, 512) ?>
                    }
                    //当前选中的表单
                    const activeForm = computed(() => formData.formList.filter(item => item.active)[0]);
                    //当前选中的表单的标题文字以及按钮文字
                    const formInfo = computed(() => {
                        const name = formData.formList.filter(item => item.active)[0].name
                        return {
                            title: titleList[name],
                            buttonText: buttonText[name]
                        }
                    });
                    //定时器
                    async function getCode(item, fn, activeFormName) {
                        if (!activeForm.value.data[0].value) {
                            activeForm.value.data[0].verify = false;
                            setText(activeForm.value.data[0], `verifyText`, <?php echo json_encode(__('saas_front::login.wangji_mima_yanzheng'), 15, 512) ?>);
                            return;
                        }
                        code_form_data.value = {
                            account: activeForm.value.data[0].value,
                            email: activeForm.value.data[0].value,
                            codeType: item.codeType,
                        };
                        if (item.isCounting) return;
                        if (isEmailorPhone({}, '', code_form_data.value.email)) {
                            return;
                        }
                        if (isgetCode) {
                            loadingKey.value = true;
                            item.isCounting = true;
                            isgetCode = false;
                            await fn.getCode(code_form_data.value.email, code_form_data.value.codeType);
                            item.verify = false;
                            setText(item, `verifyText`, <?php echo json_encode(__('saas_front::login.yanzhengma_yifasong'), 15, 512) ?> +
                                `,${code_form_data.value.email}`
                            );
                        };
                        const timer = setInterval(() => {
                            item.timeNum--;
                            item.timeText = <?php echo json_encode(__('saas_front::login.congxinfasong'), 15, 512) ?> + ` (${item.timeNum})`;
                            if (item.timeNum < 0) {
                                item.timeNum = 30;
                                item.timeText = <?php echo json_encode(__('saas_front::login.huoqu_yanzhengma'), 15, 512) ?>;
                                clearInterval(timer);
                                item.isCounting = false;
                            }
                        }, 1000);
                    }
                    //是否提交表单
                    const submitActive = computed(() => {
                        return activeForm.value.data.every(item => {
                            if (item.required === false) return true;
                            return item.value && item.verify;
                        });
                    });
                    //请求方法集合
                    const subFn = {
                        async login() {
                            loadingKey.value = true;
                            const form = {
                                account: activeForm.value.data[0].value,
                                password: activeForm.value.data[1].value,
                            };
                            if (isNum(activeForm.value.data[0].value)) {
                                form.telephone = activeForm.value.data[0].value;
                                form.calling_code = city.value;
                            } else {
                                form.email = activeForm.value.data[0].value;
                            }
                            try {
                                const res = await myaxios.post('/login/sub', form);
                                if (res?.data?.stores_count > 0) {
                                    isSuccess(res, `/account/stores`);
                                } else if (res?.data?.stores_count == 0) {
                                    isSuccess(res, `/account/stores/create?version=standard`);
                                } else {
                                    isSuccess(res, '/');
                                }
                            } catch (e) {
                                console.log(e)
                                isSuccess(e);
                            }
                        },
                        async forgot_password() {
                            code_form_data.value = {
                                account: activeForm.value.data[0].value,
                                email: activeForm.value.data[0].value,
                                codeType: `wangjimima`,
                                verify_code: activeForm.value.data[1].value,
                            };
                            loadingKey.value = true;
                            try {
                                if (isNum(code_form_data.value.email)) {
                                    code_form_data.value.telephone = code_form_data.value.email;
                                    code_form_data.value.calling_code = city.value;
                                    delete code_form_data.value.email;
                                }
                                const res = await myaxios.post('/forgotten/vcode', code_form_data
                                    .value);
                                loadingKey.value = false;
                                if (res.code === 422) {
                                    isSuccess(res);
                                    return;
                                }
                                if (res.code != 0) {
                                    isSuccess(res);
                                    return;
                                }
                                setActiveForm(2);
                            } catch (e) {
                                isSuccess(e);
                            }

                        },
                        async reset_password() {
                            code_form_data.value.password = activeForm.value.data[0].value;
                            if (isNum(code_form_data.value.email)) {
                                code_form_data.value.telephone = code_form_data.value.email;
                                code_form_data.value.calling_code = city.value;
                                delete code_form_data.value.email;
                            }
                            loadingKey.value = true;
                            try {
                                const res = await myaxios.post('/forgotten/sub', code_form_data.value);
                                if (res.code === 422) {
                                    isSuccess(res);
                                    return;
                                }
                                loadingKey.value = false;
                                changesuccess.value = 'mimasuccess';
                            } catch (e) {
                                isSuccess(e);
                            }
                        },
                        async create() {
                            try {
                                loadingKey.value = true;
                                code_form_data.value = {
                                    account: activeForm.value.data[0].value,
                                    password: activeForm.value.data[2].value,
                                    email: activeForm.value.data[0].value,
                                    codeType: `zhuce`,
                                    verify_code: activeForm.value.data[1].value,
                                    invite_code: activeForm.value.data[4].value,
                                };
                                if(urlData.coupon && activeForm.value.data[4].value === 'x2qgo3'){
                                    code_form_data.value.coupon = urlData.coupon;
                                }
                                if (isNum(code_form_data.value.email)) {
                                    code_form_data.value.telephone = code_form_data.value.email;
                                    code_form_data.value.calling_code = city.value;
                                    delete code_form_data.value.email;
                                };
                                await myaxios.post('/register/sub', code_form_data.value).then(res => {
                                    if (res.code != 0) {
                                        isSuccess(res);
                                        return;
                                    }
                                    changesuccess.value = 'zhucesuccess';
                                    loadingKey.value = false;
                                    const timer = setInterval(() => {
                                        aoutlogin.value--;
                                        if (aoutlogin.value < 0) {
                                            clearInterval(timer);
                                            toUserHome('/register/question');
                                            aoutlogin.value = 5;
                                        }
                                    }, 1000)
                                })
                            } catch (e) {
                                isSuccess(e);
                            }

                        },
                        async getCode(email, typeCode) {
                            const type = {
                                'wangjimima': 'forgotten',
                                'zhuce': 'register'
                            }
                            if (isNum(email)) {
                                return await myaxios.get(
                                        `/send_code?calling_code=${city.value}&telephone=${email}`)
                                    .then(res => {
                                        isgetCode = true;
                                        isSuccess(res);
                                    });
                                city.value = '';
                            }
                            return await myaxios.get(`/send_code?email=${email}&action=${type[typeCode]}`)
                                .then(res => {
                                    isgetCode = true;
                                    isSuccess(res);
                                });
                        },
                        async juhe_create() {
                            loadingKey.value = true;
                            const create_type = {
                                async google() {
                                    try {
                                        const res = await myaxios.post('/register/google', urlData);
                                        loadingKey.value = false;
                                        if (res.code === 0) {
                                            if (res?.data?.stores_count > 0) {
                                                isSuccess(res, '/account/stores');
                                            } else if (res?.data?.stores_count == 0) {
                                                isSuccess(res, '/account/stores/create');
                                            }else{
                                                isSuccess(res,'/account/stores/create');
                                            }
                                            return;
                                        }
                                        showMsgInElement(
                                            <?php echo json_encode(__('saas_front::login.toeken_shixiao'), 15, 512) ?> +
                                            urlData['third_channel_name'] +
                                            <?php echo json_encode(__('saas_front::login.yijingguoqi'), 15, 512) ?>

                                        )
                                        juhe_Data.value = true;

                                    } catch (e) {
                                        showMsgInElement(
                                            e.message ? e.message : JSON.stringify(e)
                                        )
                                    }
                                },
                                async facebook() {
                                    if (urlData.type == '2') {
                                        if (isNum(activeForm.value.data[0].value)) {
                                            urlData.telephone = activeForm.value.data[0].value;
                                            urlData.calling_code = city.value;
                                            delete urlData.email;
                                        } else {
                                            urlData.email = activeForm.value.data[0].value;
                                            delete urlData.telephone;
                                            delete urlData.calling_code;
                                        }
                                    }
                                    try {
                                        const res = await myaxios.post('/register/facebook',
                                            urlData);
                                        loadingKey.value = false;
                                        if (res.code === 0) {
                                            if (res?.data?.stores_count > 0) {
                                                isSuccess(res, '/account/stores');
                                            } else if (res?.data?.stores_count == 0) {
                                                isSuccess(res, '/account/stores/create');
                                            }else{
                                                isSuccess(res,'/account/stores/create');
                                            }
                                            return;
                                        }
                                        showMsgInElement(
                                            <?php echo json_encode(__('saas_front::login.toeken_shixiao'), 15, 512) ?> +
                                            urlData['third_channel_name'] +
                                            <?php echo json_encode(__('saas_front::login.yijingguoqi'), 15, 512) ?>

                                        )

                                    } catch (e) {
                                        showMsgInElement(
                                            e.message ? e.message : JSON.stringify(e)
                                        )
                                    }
                                }
                            };
                            create_type[urlData['third_channel_name']]();
                        },
                    };
                    subFn.facebook_create = subFn.juhe_create;

                    //是否使用聚合注册
                    if (urlData?.third_channel_name && urlData?.type == '1') {
                        changesuccess.value = 'juhesuccess';
                        showMsgInElement(
                            <?php echo json_encode(__('saas_front::login.shouquanchenggong'), 15, 512) ?> + ',' + <?php echo json_encode(__('saas_front::login.jijiangzhidongdenglu'), 15, 512) ?>
                        );
                        subFn.juhe_create();
                    } else if (urlData?.third_channel_name === 'facebook' && urlData?.type == '3') {
                        changesuccess.value = 'juhesuccess';
                        showMsgInElement(
                            <?php echo json_encode(__('saas_front::login.shouquanchenggong'), 15, 512) ?> + ',' + <?php echo json_encode(__('saas_front::login.jijiangzhidongdenglu'), 15, 512) ?>
                        );
                        subFn.juhe_create();
                    } else if (urlData?.third_channel_name === 'facebook' && !urlData?.email && urlData?.type ==
                        '2') {
                        setActiveForm(5);
                    } else if (urlData?.third_channel_name) {
                        setActiveForm(4);
                    } else if (urlData?.invite_code) {
                        setActiveForm(3);
                    } else {
                        setActiveForm(0);
                    };
                    //是否语言切换后的记录
                    const activeformData = {
                        create: 3,
                        forgot_password: 1,
                    }
                    if (Object.keys(activeformData).includes(urlData?.activeform)) {
                        setActiveForm(activeformData[urlData.activeform]);
                    }
                    //提交方法集合
                    function submitForm() {
                        if (!submitActive.value) return;
                        subFn[activeForm.value.name]();
                    }

                    //错误提示
                    function verofuShow(data) {
                        loadingKey.value = false;
                        if (!data) return;
                        if (data.code === 419) {
                            showMsgInElement({
                                showText: <?php echo json_encode(__('saas_front::login.yemian_guoqi'), 15, 512) ?>,
                                buttonText: <?php echo json_encode(__('saas_front::login.lijishuaxin'), 15, 512) ?>
                            }, () => {
                                location.reload();
                            });
                            return;
                        }
                        if (data.field == 'all') {
                            activeForm.value.data.forEach((item, i) => {
                                activeForm.value.data[i].verify = false;
                                if (data.message) {
                                    setText(activeForm.value.data[0], `verifyText`, data.message)
                                    return;
                                }
                                setText(activeForm.value.data[i], `verifyText`, data.data.message)
                            });
                            return;
                        };
                        const itemData = activeForm.value.data.filter(item => item.key?.some(key => key === data
                            .field))[0];
                        itemData.verify = false;
                        data.message && setText(itemData, `verifyText`, data.message);
                    }

                    //成功校验
                    function isSuccess(data, link) {
                        loadingKey.value = false;
                        if (data?.code != 0) {
                            verofuShow(data);
                        } else {
                            if (typeof link === 'number') {
                                setActiveForm(link);
                            } else if (typeof link === 'string') {                 
                                if(urlData?.ref){
                                    window.location.href = urlData.ref;
                                }else{
                                    window.location.href = `${link}${objectToQueryParams(urlData)}`
                                }
                            }
                        }
                    }
                    //切换表单
                    function setActiveForm(index) {
                        const formTypes = ['create', 'forgot_password'];
                        changesuccess.value = '';
                        formData.formList.forEach(item => {
                            item.active = false;
                            item.data.forEach(item => {
                                item.verify = true;
                                if (item.name == 'invite_code') {
                                    return
                                }
                                item.value = '';
                            });
                        });
                        formData.formList[index].active = true;
                        if (formTypes.includes(activeForm.value.name)) {
                            if (activeForm.value.name == 'create') {
                                userAgreementChange();
                            }
                         if(urlData.activeform){
                            delete urlData.activeform;
                          }

                            history.pushState({
                                    page: 'loginpage'
                                },
                                '',
                                `/login${objectToQueryParams(urlData) ? objectToQueryParams(urlData) + '&' : '?'}activeform=${activeForm.value.name}`
                            );
                            languageData.value.forEach(item => {
                                item.link = item.link + `${objectToQueryParams(urlData) ? objectToQueryParams(urlData) + '&' : '?'}activeform=${activeForm.value.name}`
                            })
                        } else {
                            history.pushState({
                                    page: 'loginpage'
                                },
                                '',
                                `/login${objectToQueryParams(urlData)}`
                            );
                            let linksData = JSON.parse(JSON.stringify(language));
                            languageData.value = linksData.map(item => {
                                item.link = item.link + objectToQueryParams(urlData);
                                return item;
                            })
                        }
                    }

                    //输入事件校验
                    function changeValue(data) {
                        data.verify = true;
                        if(data.name === 'invite_code'){
                           juhe_invite_code.value = data.value;
                        }
                        if (data.verifyCallBack && typeof data.verifyCallBack === 'function') {
                            data.verifyCallBack(); // 触发自定义校验逻辑
                        }
                    };



                    //区号相关
                    const source = reactive({
                        callingCodes: <?php echo json_encode($calling_codes, 15, 512) ?>,
                    });
                    const accountIsNumber = computed(() => {
                        if (activeForm.value.data[0].type == 'text') {
                            return /^\d+$/.test(activeForm.value.data[0].value)
                        }
                        return false;
                    })
                    const selectedCallingCode = computed(() => {
                        if (!city.value) {
                            return source.callingCodes[0]
                        }
                        return source.callingCodes.find(item => item.code === city.value);
                    });
                    //弹出用户协议确认
                    async function userAgreementChange() {
                        const userAgreementData = {
                            showText: <?php echo json_encode(__('saas_front::login.qing_tongyi_yonghuxieyi'), 15, 512) ?>,
                            btnText: <?php echo json_encode(__('saas_front::login.wo_yi_yuedu_bingtongyi'), 15, 512) ?>,
                            config: {
                                time: 10,
                                timer: null,
                                area: window.screen.width > 768 ? ['595px', '90vh'] : ['90%', '80vh']
                            }
                        }
                        if (!userAgreement) {
                            loadingKey.value = true;
                            try {
                                const res = await myaxios.get('/api/page/user_agreement');
                                userAgreement = res.data;
                                loadingKey.value = false;
                                userAgreementData.config.content = userAgreement;
                                showMsgInElement(userAgreementData,
                                    (index) => {
                                        layer.close(index);
                                        clearInterval(userAgreementData.config.timer);
                                    }
                                );
                            } catch (e) {
                                showMsgInElement(e.message ? e.message : JSON.stringify(e));
                            }
                        } else {
                            userAgreementData.config.content = userAgreement;
                            showMsgInElement(userAgreementData,
                                (index) => {
                                    layer.close(index);
                                    clearInterval(userAgreementData.config.timer);
                                }
                            );
                        }
                    }
                    watch(city, () => {
                        if (activeForm.value.name != 'login') return;
                        activeForm.value.data.forEach(item => item.verify = true);
                    });
                    return {
                        formData,
                        formInfo,
                        activeForm,
                        submitActive,
                        submitForm,
                        setActiveForm,
                        changeValue,
                        subFn,
                        getCode,
                        aoutlogin,
                        source,
                        accountIsNumber,
                        city,
                        selectedCallingCode,

                    }
                };

                function toUserHome(link) {
                    if (link) {
                        window.location.href = `${link}${objectToQueryParams(urlData)}`;
                        return
                    }
                    window.location.href = `/register/question${objectToQueryParams(urlData)}`;
                };
                //切换画面
                const changesuccess = ref('');
                return {
                    code_form_data,
                    loadingKey,
                    toUserHome,
                    changesuccess,
                    urlData,
                    JU_HE_DATA,
                    juhe_Data,
                    languageData,
                    activeLangueData,
                    juhe_invite_code,
                    objectToQueryParams,
                    ...getFormData(),
                };

            },

        });
        App.mount('#App');
        $(document).on('input', '#calling-code-search', function() {
            var value = $(this).val().toLowerCase();
            $('.code-list li').each(function() {
                var text = $(this).data('text').toLowerCase();
                if (text.indexOf(value) === -1) {
                    $(this).hide();
                } else {
                    $(this).show();
                }
            });
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('saas_front::layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\shopleadeCont\git\saas\beike\SaasFront\Providers/../Views/pages/login/login.blade.php ENDPATH**/ ?>