{"__meta": {"id": "01K4P1CGT8WFDZXG80MKQEPDJC", "datetime": "2025-09-09 09:28:00", "utime": **********.58518, "method": "GET", "uri": "/product-seoccc", "ip": "127.0.0.1"}, "php": {"version": "8.2.9", "interface": "cgi-fcgi"}, "messages": {"count": 47, "messages": [{"message": "HOOK === hook_filter: footer.content", "message_html": null, "is_string": true, "label": "log", "time": **********.645409, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === hook_filter: model.page.url", "message_html": null, "is_string": true, "label": "log", "time": **********.800805, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === hook_filter: model.page.url", "message_html": null, "is_string": true, "label": "log", "time": **********.801042, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === hook_filter: model.page.url", "message_html": null, "is_string": true, "label": "log", "time": **********.960483, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === hook_filter: model.page.url", "message_html": null, "is_string": true, "label": "log", "time": **********.960671, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === hook_filter: menu.content", "message_html": null, "is_string": true, "label": "log", "time": **********.960821, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === hook_filter: repo.product.get_detail", "message_html": null, "is_string": true, "label": "log", "time": **********.332518, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === hook_filter: resource.sku.detail", "message_html": null, "is_string": true, "label": "log", "time": **********.641943, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === hook_filter: resource.product.detail", "message_html": null, "is_string": true, "label": "log", "time": **********.101109, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === hook_filter: product.show.data", "message_html": null, "is_string": true, "label": "log", "time": **********.101539, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hook: prodcut.gift.css", "message_html": null, "is_string": true, "label": "log", "time": **********.114641, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === hook_filter: model.product.url", "message_html": null, "is_string": true, "label": "log", "time": **********.618916, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === hook_filter: model.product.url", "message_html": null, "is_string": true, "label": "log", "time": **********.619148, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hookwrapper: product.detail.name", "message_html": null, "is_string": true, "label": "log", "time": **********.942157, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hook: product.detail.price.after", "message_html": null, "is_string": true, "label": "log", "time": **********.942211, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hookwrapper: product.detail.price", "message_html": null, "is_string": true, "label": "log", "time": **********.944639, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hookwrapper: product.detail.quantity", "message_html": null, "is_string": true, "label": "log", "time": **********.945262, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hookwrapper: product.detail.sku", "message_html": null, "is_string": true, "label": "log", "time": **********.945286, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hookwrapper: product.detail.model", "message_html": null, "is_string": true, "label": "log", "time": **********.945308, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hook: product.gift", "message_html": null, "is_string": true, "label": "log", "time": **********.945322, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hookwrapper: product.detail.variables", "message_html": null, "is_string": true, "label": "log", "time": **********.945726, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hook: product.detail.buy.before", "message_html": null, "is_string": true, "label": "log", "time": **********.127262, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hookwrapper: product.detail.quantity.input", "message_html": null, "is_string": true, "label": "log", "time": **********.127331, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hookwrapper: product.detail.add_to_cart", "message_html": null, "is_string": true, "label": "log", "time": **********.127405, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hookwrapper: product.detail.buy_now", "message_html": null, "is_string": true, "label": "log", "time": **********.12746, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hook: product.detail.buy.after", "message_html": null, "is_string": true, "label": "log", "time": **********.12749, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hookwrapper: product.detail.wishlist", "message_html": null, "is_string": true, "label": "log", "time": **********.279733, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hook: product.detail.after", "message_html": null, "is_string": true, "label": "log", "time": **********.279758, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hook: product.tab.after.link", "message_html": null, "is_string": true, "label": "log", "time": **********.534576, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hook: product.tab.after.pane", "message_html": null, "is_string": true, "label": "log", "time": **********.534606, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hook: product.detail.footer", "message_html": null, "is_string": true, "label": "log", "time": **********.53462, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hook: layout.header.code", "message_html": null, "is_string": true, "label": "log", "time": **********.767699, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hookwrapper: header.top.currency", "message_html": null, "is_string": true, "label": "log", "time": **********.083782, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hookwrapper: header.top.language", "message_html": null, "is_string": true, "label": "log", "time": **********.275378, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hookwrapper: header.top.telephone", "message_html": null, "is_string": true, "label": "log", "time": **********.275424, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hookwrapper: header.menu.logo", "message_html": null, "is_string": true, "label": "log", "time": **********.275484, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hookwrapper: header.menu.icon", "message_html": null, "is_string": true, "label": "log", "time": **********.390828, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hook: header.menu.before", "message_html": null, "is_string": true, "label": "log", "time": **********.391645, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hook: header.menu.after", "message_html": null, "is_string": true, "label": "log", "time": **********.391731, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hook: footer.before", "message_html": null, "is_string": true, "label": "log", "time": **********.392554, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hook: footer.services.before", "message_html": null, "is_string": true, "label": "log", "time": **********.392577, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hook: footer.services.after", "message_html": null, "is_string": true, "label": "log", "time": **********.393208, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hook: footer.contact.before", "message_html": null, "is_string": true, "label": "log", "time": **********.393352, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hookwrapper: footer.contact", "message_html": null, "is_string": true, "label": "log", "time": **********.393405, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hook: footer.contact.after", "message_html": null, "is_string": true, "label": "log", "time": **********.393419, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hookwrapper: footer.copyright", "message_html": null, "is_string": true, "label": "log", "time": **********.393458, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hook: footer.after", "message_html": null, "is_string": true, "label": "log", "time": **********.393471, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}]}, "time": {"start": 1757381270.711407, "end": **********.585212, "duration": 9.873805046081543, "duration_str": "9.87s", "measures": [{"label": "Booting", "start": 1757381270.711407, "relative_start": 0, "end": **********.696872, "relative_end": **********.696872, "duration": 1.****************, "duration_str": "1.99s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.696881, "relative_start": 1.****************, "end": **********.585213, "relative_end": 9.5367431640625e-07, "duration": 7.***************, "duration_str": "7.89s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.70328, "relative_start": 1.***************, "end": **********.71061, "relative_end": **********.71061, "duration": 0.0073299407958984375, "duration_str": "7.33ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.102687, "relative_start": 6.***************, "end": **********.582753, "relative_end": **********.582753, "duration": 3.****************, "duration_str": "3.48s", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.583693, "relative_start": 9.***************, "end": **********.583743, "relative_end": **********.583743, "duration": 5.0067901611328125e-05, "duration_str": "50μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "33MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 17, "nb_templates": 17, "templates": [{"name": "1x product.product", "param_count": null, "params": [], "start": **********.104148, "type": "blade", "hash": "bladeD:\\shopleadeCont\\git\\saas\\themes\\fashion/product/product.blade.phpproduct.product", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fthemes%2Ffashion%2Fproduct%2Fproduct.blade.php&line=1", "ajax": false, "filename": "product.blade.php", "line": "?"}, "render_count": 1, "name_original": "product.product"}, {"name": "1x Gift::shop.prodcut_gift_css", "param_count": null, "params": [], "start": **********.115036, "type": "blade", "hash": "bladeD:\\shopleadeCont\\git\\saas\\plugins/Gift/Views/shop/prodcut_gift_css.blade.phpGift::shop.prodcut_gift_css", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fplugins%2FGift%2FViews%2Fshop%2Fprodcut_gift_css.blade.php&line=1", "ajax": false, "filename": "prodcut_gift_css.blade.php", "line": "?"}, "render_count": 1, "name_original": "Gift::shop.prodcut_gift_css"}, {"name": "1x components.breadcrumbs", "param_count": null, "params": [], "start": **********.934019, "type": "blade", "hash": "bladeD:\\shopleadeCont\\git\\saas\\themes\\default/components/breadcrumbs.blade.phpcomponents.breadcrumbs", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fthemes%2Fdefault%2Fcomponents%2Fbreadcrumbs.blade.php&line=1", "ajax": false, "filename": "breadcrumbs.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.breadcrumbs"}, {"name": "1x product.product-video", "param_count": null, "params": [], "start": **********.937842, "type": "blade", "hash": "bladeD:\\shopleadeCont\\git\\saas\\themes\\default/product/product-video.blade.phpproduct.product-video", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fthemes%2Fdefault%2Fproduct%2Fproduct-video.blade.php&line=1", "ajax": false, "filename": "product-video.blade.php", "line": "?"}, "render_count": 1, "name_original": "product.product-video"}, {"name": "1x ProductSpecial::shop.product_detail_price_after", "param_count": null, "params": [], "start": **********.942485, "type": "blade", "hash": "bladeD:\\shopleadeCont\\git\\saas\\plugins/ProductSpecial/Views/shop/product_detail_price_after.blade.phpProductSpecial::shop.product_detail_price_after", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fplugins%2FProductSpecial%2FViews%2Fshop%2Fproduct_detail_price_after.blade.php&line=1", "ajax": false, "filename": "product_detail_price_after.blade.php", "line": "?"}, "render_count": 1, "name_original": "ProductSpecial::shop.product_detail_price_after"}, {"name": "1x Gift::shop.product_gift", "param_count": null, "params": [], "start": **********.945471, "type": "blade", "hash": "bladeD:\\shopleadeCont\\git\\saas\\plugins/Gift/Views/shop/product_gift.blade.phpGift::shop.product_gift", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fplugins%2FGift%2FViews%2Fshop%2Fproduct_gift.blade.php&line=1", "ajax": false, "filename": "product_gift.blade.php", "line": "?"}, "render_count": 1, "name_original": "Gift::shop.product_gift"}, {"name": "1x GoogleAnalytics::view_product", "param_count": null, "params": [], "start": **********.944313, "type": "blade", "hash": "bladeD:\\shopleadeCont\\git\\saas\\plugins/GoogleAnalytics/Views/view_product.blade.phpGoogleAnalytics::view_product", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fplugins%2FGoogleAnalytics%2FViews%2Fview_product.blade.php&line=1", "ajax": false, "filename": "view_product.blade.php", "line": "?"}, "render_count": 1, "name_original": "GoogleAnalytics::view_product"}, {"name": "1x TikTokPixel::view_content", "param_count": null, "params": [], "start": **********.267109, "type": "blade", "hash": "bladeD:\\shopleadeCont\\git\\saas\\plugins/TikTokPixel/Views/view_content.blade.phpTikTokPixel::view_content", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fplugins%2FTikTokPixel%2FViews%2Fview_content.blade.php&line=1", "ajax": false, "filename": "view_content.blade.php", "line": "?"}, "render_count": 1, "name_original": "TikTokPixel::view_content"}, {"name": "1x TwitterPixel::view_product_content", "param_count": null, "params": [], "start": **********.764622, "type": "blade", "hash": "bladeD:\\shopleadeCont\\git\\saas\\plugins/TwitterPixel/Views/view_product_content.blade.phpTwitterPixel::view_product_content", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fplugins%2FTwitterPixel%2FViews%2Fview_product_content.blade.php&line=1", "ajax": false, "filename": "view_product_content.blade.php", "line": "?"}, "render_count": 1, "name_original": "TwitterPixel::view_product_content"}, {"name": "1x layout.master", "param_count": null, "params": [], "start": **********.76707, "type": "blade", "hash": "bladeD:\\shopleadeCont\\git\\saas\\themes\\fashion/layout/master.blade.phplayout.master", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fthemes%2Ffashion%2Flayout%2Fmaster.blade.php&line=1", "ajax": false, "filename": "master.blade.php", "line": "?"}, "render_count": 1, "name_original": "layout.master"}, {"name": "1x GoogleAnalytics::layout_header_code", "param_count": null, "params": [], "start": **********.767769, "type": "blade", "hash": "bladeD:\\shopleadeCont\\git\\saas\\plugins/GoogleAnalytics/Views/layout_header_code.blade.phpGoogleAnalytics::layout_header_code", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fplugins%2FGoogleAnalytics%2FViews%2Flayout_header_code.blade.php&line=1", "ajax": false, "filename": "layout_header_code.blade.php", "line": "?"}, "render_count": 1, "name_original": "GoogleAnalytics::layout_header_code"}, {"name": "1x TikTokPixel::layout_header_code", "param_count": null, "params": [], "start": **********.082462, "type": "blade", "hash": "bladeD:\\shopleadeCont\\git\\saas\\plugins/TikTokPixel/Views/layout_header_code.blade.phpTikTokPixel::layout_header_code", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fplugins%2FTikTokPixel%2FViews%2Flayout_header_code.blade.php&line=1", "ajax": false, "filename": "layout_header_code.blade.php", "line": "?"}, "render_count": 1, "name_original": "TikTokPixel::layout_header_code"}, {"name": "1x SaleSmartly::layout_header_code", "param_count": null, "params": [], "start": **********.082811, "type": "blade", "hash": "bladeD:\\shopleadeCont\\git\\saas\\plugins/SaleSmartly/Views/layout_header_code.blade.phpSaleSmartly::layout_header_code", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fplugins%2FSaleSmartly%2FViews%2Flayout_header_code.blade.php&line=1", "ajax": false, "filename": "layout_header_code.blade.php", "line": "?"}, "render_count": 1, "name_original": "SaleSmartly::layout_header_code"}, {"name": "1x TwitterPixel::layout_header_code", "param_count": null, "params": [], "start": **********.082997, "type": "blade", "hash": "bladeD:\\shopleadeCont\\git\\saas\\plugins/TwitterPixel/Views/layout_header_code.blade.phpTwitterPixel::layout_header_code", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fplugins%2FTwitterPixel%2FViews%2Flayout_header_code.blade.php&line=1", "ajax": false, "filename": "layout_header_code.blade.php", "line": "?"}, "render_count": 1, "name_original": "TwitterPixel::layout_header_code"}, {"name": "1x layout.header", "param_count": null, "params": [], "start": **********.083223, "type": "blade", "hash": "bladeD:\\shopleadeCont\\git\\saas\\themes\\fashion/layout/header.blade.phplayout.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fthemes%2Ffashion%2Flayout%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "layout.header"}, {"name": "1x shared.menu-pc", "param_count": null, "params": [], "start": **********.391273, "type": "blade", "hash": "bladeD:\\shopleadeCont\\git\\saas\\themes\\fashion/shared/menu-pc.blade.phpshared.menu-pc", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fthemes%2Ffashion%2Fshared%2Fmenu-pc.blade.php&line=1", "ajax": false, "filename": "menu-pc.blade.php", "line": "?"}, "render_count": 1, "name_original": "shared.menu-pc"}, {"name": "1x layout.footer", "param_count": null, "params": [], "start": **********.392172, "type": "blade", "hash": "bladeD:\\shopleadeCont\\git\\saas\\themes\\fashion/layout/footer.blade.phplayout.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fthemes%2Ffashion%2Flayout%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "layout.footer"}]}, "route": {"uri": "GET product-{slug}", "middleware": "shop", "controller": "Plugin\\SeoUrl\\Controllers\\ProductController@show<a href=\"phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fplugins%2FSeoUrl%2FControllers%2FProductController.php&line=29\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "shop.products.slug", "file": "<a href=\"phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fplugins%2FSeoUrl%2FControllers%2FProductController.php&line=29\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">plugins/SeoUrl/Controllers/ProductController.php:29-57</a>"}, "queries": {"count": 44, "nb_statements": 44, "nb_visible_statements": 44, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 7.740490000000001, "accumulated_duration_str": "7.74s", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select count(*) as aggregate from `saas_commissions` where `saas_store_id` = 88 and `status` = 1 and `total` >= '0.01' and `paid_at` is null", "type": "query", "params": [], "bindings": [88, 1, "0.01"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/CheckStoreExpired.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\CheckStoreExpired.php", "line": 29}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 119}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 805}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 784}], "start": **********.714609, "duration": 0.18713, "duration_str": "187ms", "memory": 0, "memory_str": null, "filename": "CheckStoreExpired.php:29", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/CheckStoreExpired.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\CheckStoreExpired.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fapp%2FHttp%2FMiddleware%2FCheckStoreExpired.php&line=29", "ajax": false, "filename": "CheckStoreExpired.php", "line": "29"}, "connection": "wintoshop", "explain": null, "start_percent": 0, "width_percent": 2.418}, {"sql": "select * from `languages` where `status` = 1 and `languages`.`store_id` = 88 order by `sort_order` asc", "type": "query", "params": [], "bindings": [1, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "beike/Repositories/LanguageRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\LanguageRepo.php", "line": 101}, {"index": 16, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 491}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 121}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 64}], "start": **********.9066741, "duration": 0.20411, "duration_str": "204ms", "memory": 0, "memory_str": null, "filename": "LanguageRepo.php:101", "source": {"index": 15, "namespace": null, "name": "beike/Repositories/LanguageRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\LanguageRepo.php", "line": 101}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FRepositories%2FLanguageRepo.php&line=101", "ajax": false, "filename": "LanguageRepo.php", "line": "101"}, "connection": "wintoshop", "explain": null, "start_percent": 2.418, "width_percent": 2.637}, {"sql": "select * from `languages` where `status` = 1 and `languages`.`store_id` = 88 order by `sort_order` asc", "type": "query", "params": [], "bindings": [1, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "beike/Repositories/LanguageRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\LanguageRepo.php", "line": 101}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 45}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 25}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 19, "namespace": null, "name": "app/Http/Middleware/SetLocaleFromSession.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\SetLocaleFromSession.php", "line": 37}], "start": 1757381273.113461, "duration": 0.20038999999999998, "duration_str": "200ms", "memory": 0, "memory_str": null, "filename": "LanguageRepo.php:101", "source": {"index": 15, "namespace": null, "name": "beike/Repositories/LanguageRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\LanguageRepo.php", "line": 101}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FRepositories%2FLanguageRepo.php&line=101", "ajax": false, "filename": "LanguageRepo.php", "line": "101"}, "connection": "wintoshop", "explain": null, "start_percent": 5.054, "width_percent": 2.589}, {"sql": "select * from `pages` where `active` = 1 and `pages`.`id` = 21 and `pages`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [1, 21, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 157}, {"index": 24, "namespace": null, "name": "beike/Repositories/FooterRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\FooterRepo.php", "line": 31}, {"index": 25, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 47}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 25}], "start": 1757381273.315816, "duration": 0.1529, "duration_str": "153ms", "memory": 0, "memory_str": null, "filename": "Url.php:68", "source": {"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FLibraries%2FUrl.php&line=68", "ajax": false, "filename": "Url.php", "line": "68"}, "connection": "wintoshop", "explain": null, "start_percent": 7.643, "width_percent": 1.975}, {"sql": "select * from `pages` where `active` = 1 and `pages`.`id` = 18 and `pages`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [1, 18, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 157}, {"index": 24, "namespace": null, "name": "beike/Repositories/FooterRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\FooterRepo.php", "line": 31}, {"index": 25, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 47}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 25}], "start": 1757381273.4698331, "duration": 0.2517, "duration_str": "252ms", "memory": 0, "memory_str": null, "filename": "Url.php:68", "source": {"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FLibraries%2FUrl.php&line=68", "ajax": false, "filename": "Url.php", "line": "68"}, "connection": "wintoshop", "explain": null, "start_percent": 9.619, "width_percent": 3.252}, {"sql": "select `id` from `pages` where `pages`.`store_id` = 88 order by `id` desc", "type": "query", "params": [], "bindings": [88], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "beike/Repositories/PageRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\PageRepo.php", "line": 96}, {"index": 16, "namespace": null, "name": "beike/Repositories/PageRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\PageRepo.php", "line": 80}, {"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 123}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 170}, {"index": 24, "namespace": null, "name": "beike/Repositories/FooterRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\FooterRepo.php", "line": 31}], "start": 1757381273.722472, "duration": 0.20367, "duration_str": "204ms", "memory": 0, "memory_str": null, "filename": "PageRepo.php:96", "source": {"index": 15, "namespace": null, "name": "beike/Repositories/PageRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\PageRepo.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FRepositories%2FPageRepo.php&line=96", "ajax": false, "filename": "PageRepo.php", "line": "96"}, "connection": "wintoshop", "explain": null, "start_percent": 12.87, "width_percent": 2.631}, {"sql": "select * from `page_descriptions` where `locale` = 'zh_cn' and `page_descriptions`.`page_id` in (10, 11)", "type": "query", "params": [], "bindings": ["zh_cn"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "beike/Repositories/PageRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\PageRepo.php", "line": 96}, {"index": 21, "namespace": null, "name": "beike/Repositories/PageRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\PageRepo.php", "line": 80}, {"index": 22, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 123}, {"index": 23, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 170}, {"index": 29, "namespace": null, "name": "beike/Repositories/FooterRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\FooterRepo.php", "line": 31}], "start": 1757381273.930285, "duration": 0.22069, "duration_str": "221ms", "memory": 0, "memory_str": null, "filename": "PageRepo.php:96", "source": {"index": 20, "namespace": null, "name": "beike/Repositories/PageRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\PageRepo.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FRepositories%2FPageRepo.php&line=96", "ajax": false, "filename": "PageRepo.php", "line": "96"}, "connection": "wintoshop", "explain": null, "start_percent": 15.502, "width_percent": 2.851}, {"sql": "select * from `pages` where `active` = 1 and `pages`.`id` = 12 and `pages`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [1, 12, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 157}, {"index": 24, "namespace": null, "name": "beike/Repositories/FooterRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\FooterRepo.php", "line": 31}, {"index": 25, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 47}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 25}], "start": **********.15205, "duration": 0.18350999999999998, "duration_str": "184ms", "memory": 0, "memory_str": null, "filename": "Url.php:68", "source": {"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FLibraries%2FUrl.php&line=68", "ajax": false, "filename": "Url.php", "line": "68"}, "connection": "wintoshop", "explain": null, "start_percent": 18.353, "width_percent": 2.371}, {"sql": "select * from `pages` where `active` = 1 and `pages`.`id` = 20 and `pages`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [1, 20, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 157}, {"index": 24, "namespace": null, "name": "beike/Repositories/FooterRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\FooterRepo.php", "line": 31}, {"index": 25, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 47}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 25}], "start": **********.3391829, "duration": 0.11468, "duration_str": "115ms", "memory": 0, "memory_str": null, "filename": "Url.php:68", "source": {"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FLibraries%2FUrl.php&line=68", "ajax": false, "filename": "Url.php", "line": "68"}, "connection": "wintoshop", "explain": null, "start_percent": 20.723, "width_percent": 1.482}, {"sql": "select * from `pages` where `active` = 1 and `pages`.`id` = 21 and `pages`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [1, 21, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 157}, {"index": 24, "namespace": null, "name": "beike/Repositories/FooterRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\FooterRepo.php", "line": 31}, {"index": 25, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 47}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 25}], "start": **********.454771, "duration": 0.18943000000000002, "duration_str": "189ms", "memory": 0, "memory_str": null, "filename": "Url.php:68", "source": {"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FLibraries%2FUrl.php&line=68", "ajax": false, "filename": "Url.php", "line": "68"}, "connection": "wintoshop", "explain": null, "start_percent": 22.205, "width_percent": 2.447}, {"sql": "select * from `pages` where `active` = 1 and `pages`.`id` = 11 and `pages`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [1, 11, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 157}, {"index": 21, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 48}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 25}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.646728, "duration": 0.15346, "duration_str": "153ms", "memory": 0, "memory_str": null, "filename": "Url.php:68", "source": {"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FLibraries%2FUrl.php&line=68", "ajax": false, "filename": "Url.php", "line": "68"}, "connection": "wintoshop", "explain": null, "start_percent": 24.652, "width_percent": 1.983}, {"sql": "select * from `pages` where `active` = 1 and `pages`.`id` = 10 and `pages`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [1, 10, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 157}, {"index": 21, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 48}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 25}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.801438, "duration": 0.15837, "duration_str": "158ms", "memory": 0, "memory_str": null, "filename": "Url.php:68", "source": {"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FLibraries%2FUrl.php&line=68", "ajax": false, "filename": "Url.php", "line": "68"}, "connection": "wintoshop", "explain": null, "start_percent": 26.635, "width_percent": 2.046}, {"sql": "select * from `products` where `slug` = 'seoccc' and `products`.`deleted_at` is null and `products`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": ["seoccc", 88], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "plugins/SeoUrl/Controllers/ProductController.php", "file": "D:\\shopleadeCont\\git\\saas\\plugins\\SeoUrl\\Controllers\\ProductController.php", "line": 39}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.962682, "duration": 0.*****************, "duration_str": "192ms", "memory": 0, "memory_str": null, "filename": "ProductController.php:39", "source": {"index": 17, "namespace": null, "name": "plugins/SeoUrl/Controllers/ProductController.php", "file": "D:\\shopleadeCont\\git\\saas\\plugins\\SeoUrl\\Controllers\\ProductController.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fplugins%2FSeoUrl%2FControllers%2FProductController.php&line=39", "ajax": false, "filename": "ProductController.php", "line": "39"}, "connection": "wintoshop", "explain": null, "start_percent": 28.681, "width_percent": 2.483}, {"sql": "select `products`.*, `product_relations`.`product_id` as `pivot_product_id`, `product_relations`.`relation_id` as `pivot_relation_id`, `product_relations`.`created_at` as `pivot_created_at`, `product_relations`.`updated_at` as `pivot_updated_at` from `products` inner join `product_relations` on `products`.`id` = `product_relations`.`relation_id` where `product_relations`.`product_id` = 100217 and `products`.`deleted_at` is null and `products`.`store_id` = 88", "type": "query", "params": [], "bindings": [100217, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "plugins/SeoUrl/Controllers/ProductController.php", "file": "D:\\shopleadeCont\\git\\saas\\plugins\\SeoUrl\\Controllers\\ProductController.php", "line": 42}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.1609328, "duration": 0.19899, "duration_str": "199ms", "memory": 0, "memory_str": null, "filename": "ProductController.php:42", "source": {"index": 20, "namespace": null, "name": "plugins/SeoUrl/Controllers/ProductController.php", "file": "D:\\shopleadeCont\\git\\saas\\plugins\\SeoUrl\\Controllers\\ProductController.php", "line": 42}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fplugins%2FSeoUrl%2FControllers%2FProductController.php&line=42", "ajax": false, "filename": "ProductController.php", "line": "42"}, "connection": "wintoshop", "explain": null, "start_percent": 31.164, "width_percent": 2.571}, {"sql": "select * from `product_descriptions` where `locale` = 'zh_cn' and `product_descriptions`.`product_id` in (100217)", "type": "query", "params": [], "bindings": ["zh_cn"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "beike/Repositories/ProductRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\ProductRepo.php", "line": 41}, {"index": 21, "namespace": null, "name": "plugins/SeoUrl/Controllers/ProductController.php", "file": "D:\\shopleadeCont\\git\\saas\\plugins\\SeoUrl\\Controllers\\ProductController.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.363711, "duration": 0.2006, "duration_str": "201ms", "memory": 0, "memory_str": null, "filename": "ProductRepo.php:41", "source": {"index": 20, "namespace": null, "name": "beike/Repositories/ProductRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\ProductRepo.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FRepositories%2FProductRepo.php&line=41", "ajax": false, "filename": "ProductRepo.php", "line": "41"}, "connection": "wintoshop", "explain": null, "start_percent": 33.735, "width_percent": 2.592}, {"sql": "select * from `product_skus` where `product_skus`.`product_id` in (100217) and `product_skus`.`store_id` = 88", "type": "query", "params": [], "bindings": [88], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "beike/Repositories/ProductRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\ProductRepo.php", "line": 41}, {"index": 21, "namespace": null, "name": "plugins/SeoUrl/Controllers/ProductController.php", "file": "D:\\shopleadeCont\\git\\saas\\plugins\\SeoUrl\\Controllers\\ProductController.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.5657449, "duration": 0.*****************, "duration_str": "114ms", "memory": 0, "memory_str": null, "filename": "ProductRepo.php:41", "source": {"index": 20, "namespace": null, "name": "beike/Repositories/ProductRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\ProductRepo.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FRepositories%2FProductRepo.php&line=41", "ajax": false, "filename": "ProductRepo.php", "line": "41"}, "connection": "wintoshop", "explain": null, "start_percent": 36.326, "width_percent": 1.47}, {"sql": "select * from `product_skus` where `is_default` = 1 and `product_skus`.`product_id` in (100217) and `product_skus`.`store_id` = 88", "type": "query", "params": [], "bindings": [1, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "beike/Repositories/ProductRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\ProductRepo.php", "line": 41}, {"index": 21, "namespace": null, "name": "plugins/SeoUrl/Controllers/ProductController.php", "file": "D:\\shopleadeCont\\git\\saas\\plugins\\SeoUrl\\Controllers\\ProductController.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.680655, "duration": 0.*****************, "duration_str": "191ms", "memory": 0, "memory_str": null, "filename": "ProductRepo.php:41", "source": {"index": 20, "namespace": null, "name": "beike/Repositories/ProductRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\ProductRepo.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FRepositories%2FProductRepo.php&line=41", "ajax": false, "filename": "ProductRepo.php", "line": "41"}, "connection": "wintoshop", "explain": null, "start_percent": 37.797, "width_percent": 2.473}, {"sql": "select * from `brands` where `brands`.`id` in (0) and `brands`.`store_id` = 88", "type": "query", "params": [], "bindings": [88], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "beike/Repositories/ProductRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\ProductRepo.php", "line": 41}, {"index": 21, "namespace": null, "name": "plugins/SeoUrl/Controllers/ProductController.php", "file": "D:\\shopleadeCont\\git\\saas\\plugins\\SeoUrl\\Controllers\\ProductController.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.8760839, "duration": 0.15387, "duration_str": "154ms", "memory": 0, "memory_str": null, "filename": "ProductRepo.php:41", "source": {"index": 20, "namespace": null, "name": "beike/Repositories/ProductRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\ProductRepo.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FRepositories%2FProductRepo.php&line=41", "ajax": false, "filename": "ProductRepo.php", "line": "41"}, "connection": "wintoshop", "explain": null, "start_percent": 40.269, "width_percent": 1.988}, {"sql": "select `products`.*, `product_relations`.`product_id` as `pivot_product_id`, `product_relations`.`relation_id` as `pivot_relation_id`, `product_relations`.`created_at` as `pivot_created_at`, `product_relations`.`updated_at` as `pivot_updated_at` from `products` inner join `product_relations` on `products`.`id` = `product_relations`.`relation_id` where `product_relations`.`product_id` in (100217) and `products`.`deleted_at` is null and `products`.`store_id` = 88", "type": "query", "params": [], "bindings": [88], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "beike/Repositories/ProductRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\ProductRepo.php", "line": 41}, {"index": 20, "namespace": null, "name": "plugins/SeoUrl/Controllers/ProductController.php", "file": "D:\\shopleadeCont\\git\\saas\\plugins\\SeoUrl\\Controllers\\ProductController.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.0310938, "duration": 0.30098, "duration_str": "301ms", "memory": 0, "memory_str": null, "filename": "ProductRepo.php:41", "source": {"index": 19, "namespace": null, "name": "beike/Repositories/ProductRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\ProductRepo.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FRepositories%2FProductRepo.php&line=41", "ajax": false, "filename": "ProductRepo.php", "line": "41"}, "connection": "wintoshop", "explain": null, "start_percent": 42.257, "width_percent": 3.888}, {"sql": "select * from `product_attributes` where `product_attributes`.`product_id` = 100217 and `product_attributes`.`product_id` is not null", "type": "query", "params": [], "bindings": [100217], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 21, "namespace": null, "name": "beike/Shop/Http/Resources/ProductDetail.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Shop\\Http\\Resources\\ProductDetail.php", "line": 24}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 24, "namespace": null, "name": "plugins/SeoUrl/Controllers/ProductController.php", "file": "D:\\shopleadeCont\\git\\saas\\plugins\\SeoUrl\\Controllers\\ProductController.php", "line": 46}], "start": **********.335856, "duration": 0.15159, "duration_str": "152ms", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "wintoshop", "explain": null, "start_percent": 46.146, "width_percent": 1.958}, {"sql": "select * from `currencies` where `status` = 1 and `currencies`.`store_id` = 88", "type": "query", "params": [], "bindings": [1, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "beike/Repositories/CurrencyRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\CurrencyRepo.php", "line": 91}, {"index": 16, "namespace": null, "name": "beike/Services/CurrencyService.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Services\\CurrencyService.php", "line": 25}, {"index": 17, "namespace": null, "name": "beike/Services/CurrencyService.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Services\\CurrencyService.php", "line": 33}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 358}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}], "start": **********.493173, "duration": 0.14625, "duration_str": "146ms", "memory": 0, "memory_str": null, "filename": "CurrencyRepo.php:91", "source": {"index": 15, "namespace": null, "name": "beike/Repositories/CurrencyRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\CurrencyRepo.php", "line": 91}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FRepositories%2FCurrencyRepo.php&line=91", "ajax": false, "filename": "CurrencyRepo.php", "line": "91"}, "connection": "wintoshop", "explain": null, "start_percent": 48.104, "width_percent": 1.889}, {"sql": "select * from `product_specials` where `date_start` < '2025-09-09 09:27:56' and `date_end` > '2025-09-09 09:27:56'", "type": "query", "params": [], "bindings": ["2025-09-09 09:27:56", "2025-09-09 09:27:56"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "plugins/ProductSpecial/Repositories/ProductSpecialRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\plugins\\ProductSpecial\\Repositories\\ProductSpecialRepo.php", "line": 101}, {"index": 16, "namespace": null, "name": "plugins/ProductSpecial/Bootstrap.php", "file": "D:\\shopleadeCont\\git\\saas\\plugins\\ProductSpecial\\Bootstrap.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/tormjens/eventy/src/Filter.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\tormjens\\eventy\\src\\Filter.php", "line": 21}, {"index": 23, "namespace": null, "name": "vendor/tormjens/eventy/src/Events.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\tormjens\\eventy\\src\\Events.php", "line": 163}, {"index": 25, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 695}], "start": **********.642835, "duration": 0.15064, "duration_str": "151ms", "memory": 0, "memory_str": null, "filename": "ProductSpecialRepo.php:101", "source": {"index": 15, "namespace": null, "name": "plugins/ProductSpecial/Repositories/ProductSpecialRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\plugins\\ProductSpecial\\Repositories\\ProductSpecialRepo.php", "line": 101}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fplugins%2FProductSpecial%2FRepositories%2FProductSpecialRepo.php&line=101", "ajax": false, "filename": "ProductSpecialRepo.php", "line": "101"}, "connection": "wintoshop", "explain": null, "start_percent": 49.993, "width_percent": 1.946}, {"sql": "select * from `customers` where `id` = 63 and `customers`.`deleted_at` is null and `customers`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [63, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 265}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 117}, {"index": 26, "namespace": null, "name": "beike/Shop/Http/Resources/ProductDetail.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Shop\\Http\\Resources\\ProductDetail.php", "line": 54}], "start": **********.795973, "duration": 0.15230000000000002, "duration_str": "152ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "wintoshop", "explain": null, "start_percent": 51.94, "width_percent": 1.968}, {"sql": "select * from `customer_wishlists` where `customer_wishlists`.`product_id` = 100217 and `customer_wishlists`.`product_id` is not null and `customer_id` = 0 limit 1", "type": "query", "params": [], "bindings": [100217, 0], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 117}, {"index": 23, "namespace": null, "name": "beike/Shop/Http/Resources/ProductDetail.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Shop\\Http\\Resources\\ProductDetail.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 26, "namespace": null, "name": "plugins/SeoUrl/Controllers/ProductController.php", "file": "D:\\shopleadeCont\\git\\saas\\plugins\\SeoUrl\\Controllers\\ProductController.php", "line": 46}], "start": **********.949836, "duration": 0.15081999999999998, "duration_str": "151ms", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:117", "source": {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=117", "ajax": false, "filename": "DelegatesToResource.php", "line": "117"}, "connection": "wintoshop", "explain": null, "start_percent": 53.907, "width_percent": 1.948}, {"sql": "select * from `products` where `products`.`id` = 100217 and `products`.`deleted_at` is null and `products`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [100217, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "beike/Shop/View/Components/Breadcrumb.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Shop\\View\\Components\\Breadcrumb.php", "line": 105}, {"index": 18, "namespace": null, "name": "beike/Shop/View/Components/Breadcrumb.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Shop\\View\\Components\\Breadcrumb.php", "line": 42}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}], "start": **********.1157742, "duration": 0.14094, "duration_str": "141ms", "memory": 0, "memory_str": null, "filename": "Breadcrumb.php:105", "source": {"index": 17, "namespace": null, "name": "beike/Shop/View/Components/Breadcrumb.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Shop\\View\\Components\\Breadcrumb.php", "line": 105}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FShop%2FView%2FComponents%2FBreadcrumb.php&line=105", "ajax": false, "filename": "Breadcrumb.php", "line": "105"}, "connection": "wintoshop", "explain": null, "start_percent": 55.856, "width_percent": 1.821}, {"sql": "select `categories`.*, `product_categories`.`product_id` as `pivot_product_id`, `product_categories`.`category_id` as `pivot_category_id`, `product_categories`.`created_at` as `pivot_created_at`, `product_categories`.`updated_at` as `pivot_updated_at` from `categories` inner join `product_categories` on `categories`.`id` = `product_categories`.`category_id` where `product_categories`.`product_id` = 100217 and `categories`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [100217, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "beike/Shop/View/Components/Breadcrumb.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Shop\\View\\Components\\Breadcrumb.php", "line": 106}, {"index": 17, "namespace": null, "name": "beike/Shop/View/Components/Breadcrumb.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Shop\\View\\Components\\Breadcrumb.php", "line": 42}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}], "start": **********.259907, "duration": 0.15155000000000002, "duration_str": "152ms", "memory": 0, "memory_str": null, "filename": "Breadcrumb.php:106", "source": {"index": 16, "namespace": null, "name": "beike/Shop/View/Components/Breadcrumb.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Shop\\View\\Components\\Breadcrumb.php", "line": 106}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FShop%2FView%2FComponents%2FBreadcrumb.php&line=106", "ajax": false, "filename": "Breadcrumb.php", "line": "106"}, "connection": "wintoshop", "explain": null, "start_percent": 57.676, "width_percent": 1.958}, {"sql": "select * from `products` where `products`.`id` = 100217 and `products`.`deleted_at` is null and `products`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [100217, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 56}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 157}, {"index": 21, "namespace": null, "name": "beike/Shop/View/Components/Breadcrumb.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Shop\\View\\Components\\Breadcrumb.php", "line": 42}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}], "start": **********.412184, "duration": 0.20618, "duration_str": "206ms", "memory": 0, "memory_str": null, "filename": "Url.php:56", "source": {"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FLibraries%2FUrl.php&line=56", "ajax": false, "filename": "Url.php", "line": "56"}, "connection": "wintoshop", "explain": null, "start_percent": 59.634, "width_percent": 2.664}, {"sql": "select * from `products` where `products`.`id` = 100217 and `products`.`deleted_at` is null and `products`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [100217, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "beike/Repositories/ProductRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\ProductRepo.php", "line": 339}, {"index": 18, "namespace": null, "name": "beike/Repositories/ProductRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\ProductRepo.php", "line": 354}, {"index": 19, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 119}, {"index": 20, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 170}, {"index": 23, "namespace": null, "name": "beike/Shop/View/Components/Breadcrumb.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Shop\\View\\Components\\Breadcrumb.php", "line": 42}], "start": **********.619557, "duration": 0.15, "duration_str": "150ms", "memory": 0, "memory_str": null, "filename": "ProductRepo.php:339", "source": {"index": 17, "namespace": null, "name": "beike/Repositories/ProductRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\ProductRepo.php", "line": 339}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FRepositories%2FProductRepo.php&line=339", "ajax": false, "filename": "ProductRepo.php", "line": "339"}, "connection": "wintoshop", "explain": null, "start_percent": 62.298, "width_percent": 1.938}, {"sql": "select * from `product_descriptions` where `product_descriptions`.`product_id` = 100217 and `product_descriptions`.`product_id` is not null and `locale` = 'zh_cn' limit 1", "type": "query", "params": [], "bindings": [100217, "zh_cn"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "beike/Repositories/ProductRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\ProductRepo.php", "line": 341}, {"index": 22, "namespace": null, "name": "beike/Repositories/ProductRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\ProductRepo.php", "line": 354}, {"index": 23, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 119}, {"index": 24, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 170}, {"index": 27, "namespace": null, "name": "beike/Shop/View/Components/Breadcrumb.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Shop\\View\\Components\\Breadcrumb.php", "line": 42}], "start": **********.770429, "duration": 0.16122, "duration_str": "161ms", "memory": 0, "memory_str": null, "filename": "ProductRepo.php:341", "source": {"index": 21, "namespace": null, "name": "beike/Repositories/ProductRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\ProductRepo.php", "line": 341}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FRepositories%2FProductRepo.php&line=341", "ajax": false, "filename": "ProductRepo.php", "line": "341"}, "connection": "wintoshop", "explain": null, "start_percent": 64.236, "width_percent": 2.083}, {"sql": "select * from `price_break_discounts` where exists (select * from `products` inner join `price_break_discount_products` on `products`.`id` = `price_break_discount_products`.`product_id` where `price_break_discounts`.`id` = `price_break_discount_products`.`price_break_discount_id` and `product_id` in (100217) and `products`.`deleted_at` is null and `products`.`store_id` = 88) and `status` = 1 and `date_from` <= '2025-09-09' and `date_to` >= '2025-09-09' and `price_break_discounts`.`store_id` = 88 order by `created_at` desc", "type": "query", "params": [], "bindings": [100217, 88, 1, "2025-09-09", "2025-09-09", 88], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "plugins/PriceBreakDiscount/Services/PriceBreakDiscountService.php", "file": "D:\\shopleadeCont\\git\\saas\\plugins\\PriceBreakDiscount\\Services\\PriceBreakDiscountService.php", "line": 97}, {"index": 16, "namespace": null, "name": "plugins/PriceBreakDiscount/Bootstrap.php", "file": "D:\\shopleadeCont\\git\\saas\\plugins\\PriceBreakDiscount\\Bootstrap.php", "line": 65}, {"index": 19, "namespace": null, "name": "beike/Hook/Hook.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Hook\\Hook.php", "line": 40}, {"index": 20, "namespace": null, "name": "beike/Hook/Hook.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Hook\\Hook.php", "line": 80}, {"index": 22, "namespace": "view", "name": "product.product", "file": "D:\\shopleadeCont\\git\\saas\\themes\\fashion/product/product.blade.php", "line": 295}], "start": **********.948101, "duration": 0.17806, "duration_str": "178ms", "memory": 0, "memory_str": null, "filename": "PriceBreakDiscountService.php:97", "source": {"index": 15, "namespace": null, "name": "plugins/PriceBreakDiscount/Services/PriceBreakDiscountService.php", "file": "D:\\shopleadeCont\\git\\saas\\plugins\\PriceBreakDiscount\\Services\\PriceBreakDiscountService.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fplugins%2FPriceBreakDiscount%2FServices%2FPriceBreakDiscountService.php&line=97", "ajax": false, "filename": "PriceBreakDiscountService.php", "line": "97"}, "connection": "wintoshop", "explain": null, "start_percent": 66.319, "width_percent": 2.3}, {"sql": "select * from `customers` where `id` = 63 and `customers`.`deleted_at` is null and `customers`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [63, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 265}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.127897, "duration": 0.15134999999999998, "duration_str": "151ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "wintoshop", "explain": null, "start_percent": 68.619, "width_percent": 1.955}, {"sql": "select * from `size_charts` where (not exists (select * from `products` inner join `size_chart_products` on `products`.`id` = `size_chart_products`.`product_id` where `size_charts`.`id` = `size_chart_products`.`size_chart_id` and `products`.`deleted_at` is null and `products`.`store_id` = 88) and `status` = 1 or exists (select * from `products` inner join `size_chart_products` on `products`.`id` = `size_chart_products`.`product_id` where `size_charts`.`id` = `size_chart_products`.`size_chart_id` and `products`.`id` = 100217 and `products`.`deleted_at` is null and `products`.`store_id` = 88)) and `size_charts`.`store_id` = 88 order by `updated_at` desc limit 1", "type": "query", "params": [], "bindings": [88, 1, 100217, 88, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "plugins/SizeChart/Repositories/SizeChartRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\plugins\\SizeChart\\Repositories\\SizeChartRepo.php", "line": 169}, {"index": 17, "namespace": null, "name": "plugins/SizeChart/Bootstrap.php", "file": "D:\\shopleadeCont\\git\\saas\\plugins\\SizeChart\\Bootstrap.php", "line": 64}, {"index": 20, "namespace": null, "name": "beike/Hook/Hook.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Hook\\Hook.php", "line": 40}, {"index": 21, "namespace": null, "name": "beike/Hook/Hook.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Hook\\Hook.php", "line": 64}, {"index": 23, "namespace": "view", "name": "product.product", "file": "D:\\shopleadeCont\\git\\saas\\themes\\fashion/product/product.blade.php", "line": 441}], "start": **********.2821121, "duration": 0.25187, "duration_str": "252ms", "memory": 0, "memory_str": null, "filename": "SizeChartRepo.php:169", "source": {"index": 16, "namespace": null, "name": "plugins/SizeChart/Repositories/SizeChartRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\plugins\\SizeChart\\Repositories\\SizeChartRepo.php", "line": 169}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fplugins%2FSizeChart%2FRepositories%2FSizeChartRepo.php&line=169", "ajax": false, "filename": "SizeChartRepo.php", "line": "169"}, "connection": "wintoshop", "explain": null, "start_percent": 70.574, "width_percent": 3.254}, {"sql": "select * from `products` where `products`.`id` = 100217 and `products`.`deleted_at` is null and `products`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [100217, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "plugins/GoogleAnalytics/Bootstrap.php", "file": "D:\\shopleadeCont\\git\\saas\\plugins\\GoogleAnalytics\\Bootstrap.php", "line": 56}, {"index": 20, "namespace": null, "name": "beike/Hook/Hook.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Hook\\Hook.php", "line": 40}, {"index": 21, "namespace": null, "name": "beike/Hook/Hook.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Hook\\Hook.php", "line": 64}, {"index": 23, "namespace": "view", "name": "product.product", "file": "D:\\shopleadeCont\\git\\saas\\themes\\fashion/product/product.blade.php", "line": 539}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.534872, "duration": 0.15463, "duration_str": "155ms", "memory": 0, "memory_str": null, "filename": "Bootstrap.php:56", "source": {"index": 17, "namespace": null, "name": "plugins/GoogleAnalytics/Bootstrap.php", "file": "D:\\shopleadeCont\\git\\saas\\plugins\\GoogleAnalytics\\Bootstrap.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fplugins%2FGoogleAnalytics%2FBootstrap.php&line=56", "ajax": false, "filename": "Bootstrap.php", "line": "56"}, "connection": "wintoshop", "explain": null, "start_percent": 73.828, "width_percent": 1.998}, {"sql": "select * from `product_skus` where `product_skus`.`product_id` = 100217 and `product_skus`.`product_id` is not null and `is_default` = 1 and `product_skus`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [100217, 1, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "plugins/GoogleAnalytics/Bootstrap.php", "file": "D:\\shopleadeCont\\git\\saas\\plugins\\GoogleAnalytics\\Bootstrap.php", "line": 59}, {"index": 24, "namespace": null, "name": "beike/Hook/Hook.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Hook\\Hook.php", "line": 40}, {"index": 25, "namespace": null, "name": "beike/Hook/Hook.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Hook\\Hook.php", "line": 64}, {"index": 27, "namespace": "view", "name": "product.product", "file": "D:\\shopleadeCont\\git\\saas\\themes\\fashion/product/product.blade.php", "line": 539}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.690611, "duration": 0.25307, "duration_str": "253ms", "memory": 0, "memory_str": null, "filename": "Bootstrap.php:59", "source": {"index": 21, "namespace": null, "name": "plugins/GoogleAnalytics/Bootstrap.php", "file": "D:\\shopleadeCont\\git\\saas\\plugins\\GoogleAnalytics\\Bootstrap.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fplugins%2FGoogleAnalytics%2FBootstrap.php&line=59", "ajax": false, "filename": "Bootstrap.php", "line": "59"}, "connection": "wintoshop", "explain": null, "start_percent": 75.826, "width_percent": 3.269}, {"sql": "select * from `products` where `products`.`id` = 100217 and `products`.`deleted_at` is null and `products`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [100217, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "plugins/TikTokPixel/Bootstrap.php", "file": "D:\\shopleadeCont\\git\\saas\\plugins\\TikTokPixel\\Bootstrap.php", "line": 58}, {"index": 20, "namespace": null, "name": "beike/Hook/Hook.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Hook\\Hook.php", "line": 40}, {"index": 21, "namespace": null, "name": "beike/Hook/Hook.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Hook\\Hook.php", "line": 64}, {"index": 23, "namespace": "view", "name": "product.product", "file": "D:\\shopleadeCont\\git\\saas\\themes\\fashion/product/product.blade.php", "line": 539}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.9448931, "duration": 0.2036, "duration_str": "204ms", "memory": 0, "memory_str": null, "filename": "Bootstrap.php:58", "source": {"index": 17, "namespace": null, "name": "plugins/TikTokPixel/Bootstrap.php", "file": "D:\\shopleadeCont\\git\\saas\\plugins\\TikTokPixel\\Bootstrap.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fplugins%2FTikTokPixel%2FBootstrap.php&line=58", "ajax": false, "filename": "Bootstrap.php", "line": "58"}, "connection": "wintoshop", "explain": null, "start_percent": 79.095, "width_percent": 2.63}, {"sql": "select * from `product_skus` where `product_skus`.`product_id` = 100217 and `product_skus`.`product_id` is not null and `is_default` = 1 and `product_skus`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [100217, 1, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "plugins/TikTokPixel/Bootstrap.php", "file": "D:\\shopleadeCont\\git\\saas\\plugins\\TikTokPixel\\Bootstrap.php", "line": 63}, {"index": 24, "namespace": null, "name": "beike/Hook/Hook.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Hook\\Hook.php", "line": 40}, {"index": 25, "namespace": null, "name": "beike/Hook/Hook.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Hook\\Hook.php", "line": 64}, {"index": 27, "namespace": "view", "name": "product.product", "file": "D:\\shopleadeCont\\git\\saas\\themes\\fashion/product/product.blade.php", "line": 539}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.150095, "duration": 0.1161, "duration_str": "116ms", "memory": 0, "memory_str": null, "filename": "Bootstrap.php:63", "source": {"index": 21, "namespace": null, "name": "plugins/TikTokPixel/Bootstrap.php", "file": "D:\\shopleadeCont\\git\\saas\\plugins\\TikTokPixel\\Bootstrap.php", "line": 63}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fplugins%2FTikTokPixel%2FBootstrap.php&line=63", "ajax": false, "filename": "Bootstrap.php", "line": "63"}, "connection": "wintoshop", "explain": null, "start_percent": 81.726, "width_percent": 1.5}, {"sql": "select * from `products` where `products`.`id` = 100217 and `products`.`deleted_at` is null and `products`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [100217, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "plugins/TwitterPixel/Bootstrap.php", "file": "D:\\shopleadeCont\\git\\saas\\plugins\\TwitterPixel\\Bootstrap.php", "line": 56}, {"index": 20, "namespace": null, "name": "beike/Hook/Hook.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Hook\\Hook.php", "line": 40}, {"index": 21, "namespace": null, "name": "beike/Hook/Hook.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Hook\\Hook.php", "line": 64}, {"index": 23, "namespace": "view", "name": "product.product", "file": "D:\\shopleadeCont\\git\\saas\\themes\\fashion/product/product.blade.php", "line": 539}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.269235, "duration": 0.18662, "duration_str": "187ms", "memory": 0, "memory_str": null, "filename": "Bootstrap.php:56", "source": {"index": 17, "namespace": null, "name": "plugins/TwitterPixel/Bootstrap.php", "file": "D:\\shopleadeCont\\git\\saas\\plugins\\TwitterPixel\\Bootstrap.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fplugins%2FTwitterPixel%2FBootstrap.php&line=56", "ajax": false, "filename": "Bootstrap.php", "line": "56"}, "connection": "wintoshop", "explain": null, "start_percent": 83.226, "width_percent": 2.411}, {"sql": "select * from `product_skus` where `product_skus`.`product_id` = 100217 and `product_skus`.`product_id` is not null and `is_default` = 1 and `product_skus`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [100217, 1, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "plugins/TwitterPixel/Bootstrap.php", "file": "D:\\shopleadeCont\\git\\saas\\plugins\\TwitterPixel\\Bootstrap.php", "line": 57}, {"index": 24, "namespace": null, "name": "beike/Hook/Hook.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Hook\\Hook.php", "line": 40}, {"index": 25, "namespace": null, "name": "beike/Hook/Hook.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Hook\\Hook.php", "line": 64}, {"index": 27, "namespace": "view", "name": "product.product", "file": "D:\\shopleadeCont\\git\\saas\\themes\\fashion/product/product.blade.php", "line": 539}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.457079, "duration": 0.1124, "duration_str": "112ms", "memory": 0, "memory_str": null, "filename": "Bootstrap.php:57", "source": {"index": 21, "namespace": null, "name": "plugins/TwitterPixel/Bootstrap.php", "file": "D:\\shopleadeCont\\git\\saas\\plugins\\TwitterPixel\\Bootstrap.php", "line": 57}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fplugins%2FTwitterPixel%2FBootstrap.php&line=57", "ajax": false, "filename": "Bootstrap.php", "line": "57"}, "connection": "wintoshop", "explain": null, "start_percent": 85.637, "width_percent": 1.452}, {"sql": "select * from `product_descriptions` where `product_descriptions`.`product_id` = 100217 and `product_descriptions`.`product_id` is not null and `locale` = 'zh_cn' limit 1", "type": "query", "params": [], "bindings": [100217, "zh_cn"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "plugins/TwitterPixel/Bootstrap.php", "file": "D:\\shopleadeCont\\git\\saas\\plugins\\TwitterPixel\\Bootstrap.php", "line": 62}, {"index": 24, "namespace": null, "name": "beike/Hook/Hook.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Hook\\Hook.php", "line": 40}, {"index": 25, "namespace": null, "name": "beike/Hook/Hook.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Hook\\Hook.php", "line": 64}, {"index": 27, "namespace": "view", "name": "product.product", "file": "D:\\shopleadeCont\\git\\saas\\themes\\fashion/product/product.blade.php", "line": 539}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.5705621, "duration": 0.19321000000000002, "duration_str": "193ms", "memory": 0, "memory_str": null, "filename": "Bootstrap.php:62", "source": {"index": 21, "namespace": null, "name": "plugins/TwitterPixel/Bootstrap.php", "file": "D:\\shopleadeCont\\git\\saas\\plugins\\TwitterPixel\\Bootstrap.php", "line": 62}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fplugins%2FTwitterPixel%2FBootstrap.php&line=62", "ajax": false, "filename": "Bootstrap.php", "line": "62"}, "connection": "wintoshop", "explain": null, "start_percent": 87.089, "width_percent": 2.496}, {"sql": "select * from `customers` where `id` = 63 and `customers`.`deleted_at` is null and `customers`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [63, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 265}, {"index": 22, "namespace": null, "name": "beike/Hook/Hook.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Hook\\Hook.php", "line": 40}, {"index": 23, "namespace": null, "name": "beike/Hook/Hook.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Hook\\Hook.php", "line": 64}], "start": **********.768229, "duration": 0.19968, "duration_str": "200ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "wintoshop", "explain": null, "start_percent": 89.585, "width_percent": 2.58}, {"sql": "select * from `customers` where `id` = 63 and `customers`.`deleted_at` is null and `customers`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [63, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 265}, {"index": 22, "namespace": null, "name": "beike/Hook/Hook.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Hook\\Hook.php", "line": 40}, {"index": 23, "namespace": null, "name": "beike/Hook/Hook.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Hook\\Hook.php", "line": 64}], "start": **********.96864, "duration": 0.11337, "duration_str": "113ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "wintoshop", "explain": null, "start_percent": 92.164, "width_percent": 1.465}, {"sql": "select * from `languages` where `code` = 'zh_cn' and `languages`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": ["zh_cn", 88], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 503}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.0840318, "duration": 0.19074000000000002, "duration_str": "191ms", "memory": 0, "memory_str": null, "filename": "Helpers.php:503", "source": {"index": 16, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 503}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FHelpers.php&line=503", "ajax": false, "filename": "Helpers.php", "line": "503"}, "connection": "wintoshop", "explain": null, "start_percent": 93.629, "width_percent": 2.464}, {"sql": "select * from `customers` where `id` = 63 and `customers`.`deleted_at` is null and `customers`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [63, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "view", "name": "layout.header", "file": "D:\\shopleadeCont\\git\\saas\\themes\\fashion/layout/header.blade.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.275871, "duration": 0.11414, "duration_str": "114ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "wintoshop", "explain": null, "start_percent": 96.093, "width_percent": 1.475}, {"sql": "select * from `customers` where `id` = 63 and `customers`.`deleted_at` is null and `customers`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [63, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 265}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.393736, "duration": 0.18825999999999998, "duration_str": "188ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "wintoshop", "explain": null, "start_percent": 97.568, "width_percent": 2.432}]}, "models": {"data": {"Beike\\Models\\Product": {"value": 7, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "Beike\\Models\\Language": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "Beike\\Models\\ProductSku": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FModels%2FProductSku.php&line=1", "ajax": false, "filename": "ProductSku.php", "line": "?"}}, "Beike\\Models\\Page": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FModels%2FPage.php&line=1", "ajax": false, "filename": "Page.php", "line": "?"}}, "Beike\\Models\\ProductDescription": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FModels%2FProductDescription.php&line=1", "ajax": false, "filename": "ProductDescription.php", "line": "?"}}, "Beike\\Models\\Currency": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "Beike\\Models\\PageDescription": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FModels%2FPageDescription.php&line=1", "ajax": false, "filename": "PageDescription.php", "line": "?"}}}, "count": 29, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "O4jG04i8Bcl9bpHOIqVGqqnZWApkEJZpBCBit84f", "locale": "zh_cn", "login_web_shop_59ba36addc2b2f9401580f014c7f58ea4e30989d": "63", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://newshop.shopleade.test/product-seoccc\"\n]", "url": "array:1 [\n  \"intended\" => \"http://jiuyia.shopleade.test/admin\"\n]", "histories": "array:6 [\n  0 => \"admin.theme.index\"\n  1 => \"admin.plugins.index\"\n  2 => \"admin.home.index\"\n  3 => \"admin.products.index\"\n  4 => \"admin.plugins.theme\"\n  5 => \"admin.settings.index\"\n]", "saas_front_locale": "zh_cn", "login_web_saas_user_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "page": "1", "login_web_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "74", "currency": "USD"}, "request": {"data": {"status": "200 OK", "full_url": "http://newshop.shopleade.test/product-seoccc", "action_name": "shop.products.slug", "controller_action": "Plugin\\SeoUrl\\Controllers\\ProductController@show", "uri": "GET product-{slug}", "controller": "Plugin\\SeoUrl\\Controllers\\ProductController@show<a href=\"phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fplugins%2FSeoUrl%2FControllers%2FProductController.php&line=29\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fplugins%2FSeoUrl%2FControllers%2FProductController.php&line=29\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">plugins/SeoUrl/Controllers/ProductController.php:29-57</a>", "middleware": "shop", "duration": "9.88s", "peak_memory": "36MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-2049806815 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2049806815\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-463509852 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-463509852\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-331216412 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"898 characters\">_ss_s_uid=df387fd27bfaa61c2967bdd458dc2053; beike_version={%22current%22:%*********%22%2C%22latest%22:%221.5.6%22%2C%22release_date%22:%222025-05-08%22%2C%22has_new_version%22:true}; XSRF-TOKEN=eyJpdiI6IldmcTVMQnJtb2hBcnQzMGcySkw2UVE9PSIsInZhbHVlIjoidmVmNmYyMFYyRGpuWkwvUFdxZjNydTlvTVk3OURFdXVzNmN0K0doN3Q2M21jK2YrSEE3REUreEdCTFVSNE9xRlJZNWI3eElCL3dzaFEzMU9EYnN1bTVqamt1eHpQN3lvYm15N3F2ZC9OQ0MxaUh0b0pUS2FpNURra1lEUHpwc3MiLCJtYWMiOiJjZDZiMDUwM2RhNDM0ZDQ4ZGU1MTdhNTgyYzg0MjVkY2MxYzVmMzU5YmQ5MDAxODJjN2M4MTRjYTQzZTVlNjIxIiwidGFnIjoiIn0%3D; shopleade_session=eyJpdiI6ImdCb1M2eXZJM3lncDB6Z0lkRUI0Zmc9PSIsInZhbHVlIjoiTTZnOVRQK2hHaWx3ZVBjdDd6OUc2ZXNNWGg1OUVIUnBWUEdJZ29DbmJCTXVRd3VxSmxRamZ0cVFkYWRsM040R0VYYUVBejRhNTRJcDR0R2lyR3MxNkk5RFZGWnpENVRGeGdudmNkd1kzdit6bVJoNW84WmlJRlp5Wml4VHZ1Y0QiLCJtYWMiOiIwZTQ4ODNiZGZhY2Y5ODU2MGQyMDA2OWE3Yzk0OGRlODhiYjJjZGNjNTQ3ZDI3MTUxNDJjMmQ0Yjk3YTg5YTI2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">zh-CN,zh;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"45 characters\">http://newshop.shopleade.test/latest_products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">newshop.shopleade.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-331216412\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1437374738 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_ss_s_uid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>beike_version</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">O4jG04i8Bcl9bpHOIqVGqqnZWApkEJZpBCBit84f</span>\"\n  \"<span class=sf-dump-key>shopleade_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YkGz0mA2y0BMpk6lkQ8Dkf8G3sQ6bHa4Ejf6HE7d</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1437374738\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-461435761 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 09 Sep 2025 01:27:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"455 characters\">XSRF-TOKEN=eyJpdiI6IitMNWI3Znppc0VjR2JUUVpOM0d3UVE9PSIsInZhbHVlIjoiOTIvV1lwYWxPenhlc3V5M0xtR1RRZGRkdkQwVm1teDhwQU80SGtsZlhRZHBtVFhkOS92NHdZOTh6K2VpM1Z4VFRKS0dyYkJnUzZxbFdIc1FaQXFEbHl4Z1V4UFozRG80Z2xHQkhibnBDRVIram5jUGFTNEZGRWJQTGo1Z1h4RnIiLCJtYWMiOiI5YTAzZWI0OGE3MDA1MDExMzk3ZWMxNzQ1MzYxZGFkY2NjNTRlZmNiM2RiNGJmMGM2ZDI0NzAwYTJmMDhkYzQxIiwidGFnIjoiIn0%3D; expires=Thu, 09 Oct 2025 01:28:00 GMT; Max-Age=2592000; path=/; domain=.shopleade.test; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"472 characters\">shopleade_session=eyJpdiI6IlRzcm5tdHhaQXVvMFNCYVJZVWFOY3c9PSIsInZhbHVlIjoiWEhONkVYV3NLYlhkdGpyYURueGNzMjlDUG8ramdpNHMvUW15TDFaM3E3OUZjbmdMZWZ1NkZob2NyMzNNN2YyOElOTkN2SHF5bXIwYWppK1piOElSZXliOHE5SURoTWk1ZjE3RkhUWVJzL3RlQWdyM2FJeEptYmxGSlB0WTBaSDciLCJtYWMiOiI5MWM4YmMzOTA2NmY2NmE4Y2Q2NjZiNGI4OGJhYjFlNGExYmQwZTRkNGNmZjAxNDJjY2FmMDgxYzE4ZjYyZjk0IiwidGFnIjoiIn0%3D; expires=Thu, 09 Oct 2025 01:28:00 GMT; Max-Age=2592000; path=/; domain=.shopleade.test; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"424 characters\">XSRF-TOKEN=eyJpdiI6IitMNWI3Znppc0VjR2JUUVpOM0d3UVE9PSIsInZhbHVlIjoiOTIvV1lwYWxPenhlc3V5M0xtR1RRZGRkdkQwVm1teDhwQU80SGtsZlhRZHBtVFhkOS92NHdZOTh6K2VpM1Z4VFRKS0dyYkJnUzZxbFdIc1FaQXFEbHl4Z1V4UFozRG80Z2xHQkhibnBDRVIram5jUGFTNEZGRWJQTGo1Z1h4RnIiLCJtYWMiOiI5YTAzZWI0OGE3MDA1MDExMzk3ZWMxNzQ1MzYxZGFkY2NjNTRlZmNiM2RiNGJmMGM2ZDI0NzAwYTJmMDhkYzQxIiwidGFnIjoiIn0%3D; expires=Thu, 09-Oct-2025 01:28:00 GMT; domain=.shopleade.test; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">shopleade_session=eyJpdiI6IlRzcm5tdHhaQXVvMFNCYVJZVWFOY3c9PSIsInZhbHVlIjoiWEhONkVYV3NLYlhkdGpyYURueGNzMjlDUG8ramdpNHMvUW15TDFaM3E3OUZjbmdMZWZ1NkZob2NyMzNNN2YyOElOTkN2SHF5bXIwYWppK1piOElSZXliOHE5SURoTWk1ZjE3RkhUWVJzL3RlQWdyM2FJeEptYmxGSlB0WTBaSDciLCJtYWMiOiI5MWM4YmMzOTA2NmY2NmE4Y2Q2NjZiNGI4OGJhYjFlNGExYmQwZTRkNGNmZjAxNDJjY2FmMDgxYzE4ZjYyZjk0IiwidGFnIjoiIn0%3D; expires=Thu, 09-Oct-2025 01:28:00 GMT; domain=.shopleade.test; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-461435761\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-50874720 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">O4jG04i8Bcl9bpHOIqVGqqnZWApkEJZpBCBit84f</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"5 characters\">zh_cn</span>\"\n  \"<span class=sf-dump-key>login_web_shop_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>63</span>\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://newshop.shopleade.test/product-seoccc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://jiuyia.shopleade.test/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>histories</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">admin.theme.index</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"19 characters\">admin.plugins.index</span>\"\n    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">admin.home.index</span>\"\n    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"20 characters\">admin.products.index</span>\"\n    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"19 characters\">admin.plugins.theme</span>\"\n    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"20 characters\">admin.settings.index</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>saas_front_locale</span>\" => \"<span class=sf-dump-str title=\"5 characters\">zh_cn</span>\"\n  \"<span class=sf-dump-key>login_web_saas_user_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>login_web_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">USD</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-50874720\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://newshop.shopleade.test/product-seoccc", "action_name": "shop.products.slug", "controller_action": "Plugin\\SeoUrl\\Controllers\\ProductController@show"}, "badge": null}}