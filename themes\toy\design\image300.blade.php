<section class="module-item {{ $design ? 'module-item-design' : '' }}" id="module-{{ $module_id }}">
    @include('design._partial._module_tool')
    <div class="module-image-banner banner-magnify-hover module-info">
        <div class="{{ !$content['full'] ? 'container' : '' }}">
            <div class="image-wrap-cont">
                @foreach ($content['images'] as $item)
                    <a class="image-wrap" href="{{ $item['link']['link'] ?: 'javascript:void(0)' }}">
                        <img src="{{ $item['image'] }}" class="img-fluid">
                                               <div class="image-title-cont">
                            @if (!empty($item['title']))
                                <h2 class="title">{{ $item['title'] }}</h2>
                            @endif
                            @if (!empty($item['description']))
                                <p class="description">{{ $item['description'] }}</p>
                            @endif
                            @if (!empty($item['sub_title']))
                                <p class="sub-title">{{ $item['sub_title'] }}</p>
                            @endif
                        </div>
                    </a>
                @endforeach
            </div>
        </div>
    </div>
    <script src="/vendor/gsap/gsap.min.js"></script>
    <script src="/vendor/gsap/ScrollTrigger.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            gsap.registerPlugin(ScrollTrigger);
            const moduleElement = document.querySelector('#module-{{ $module_id }}');
            if (moduleElement) {
                const images = moduleElement.querySelectorAll('.image-wrap');
                
                if (images.length > 0) {
                    gsap.set(images, { opacity: 0, y: 50 });
                    ScrollTrigger.create({
                        trigger: moduleElement,
                        start: "top 80%",
                        onEnter: () => {
                            gsap.to(images, {
                                opacity: 1,
                                y: 0,
                                duration: 0.8,
                                ease: "power3.out",
                                stagger: 0.2 
                            });
                        },
                        once: true 
                    });
                }
            } else {
                console.error('Module element #module-{{ $module_id }} not found.');
            }
        });
    </script>
</section>