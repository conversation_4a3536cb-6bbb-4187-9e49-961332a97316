<footer class="footer-wrap">
  <div class="container">
    <div class="footer-content mb-4">
      <div class="footer-list">
        <div class="footer-list-title">SHOPLEADE</div>
        <ul>
          <li><a href="/about_us?lang=<?php echo e(saas_front_locale()); ?>"><?php echo e(__('saas_front::home.footer_item_1_list_1')); ?></a></li>
          
          <li><a href="/webpages/customer_cases"><?php echo e(__('saas_front::home.footer_item_1_list_3')); ?></a></li>
          <li><a href="/partners"><?php echo e(__('saas_front::home.footer_item_1_list_4')); ?></a></li>
          <li><a href="/webpages/friendship_links"><?php echo e(__('saas_front::home.footer_item_1_list_5')); ?></a></li>
        </ul>
      </div>
      <div class="footer-list">
        <div class="footer-list-title"><?php echo e(__('saas_front::home.footer_item_2_title')); ?></div>
        <ul>
          <li><a href="/video"><?php echo e(__('saas_front::home.footer_item_2_list_1')); ?></a></li>
          <li><a href="/marketing"><?php echo e(__('saas_front::home.footer_item_2_list_2')); ?></a></li>
          <li><a id="helper_link" href="<?php echo e(env('HELP_URL')); ?>/<?php echo e(saas_front_locale()); ?>"><?php echo e(__('saas_front::home.footer_item_2_list_3')); ?></a></li>
          
          
        </ul>
      </div>
      <div class="footer-list">
        <div class="footer-list-title"><?php echo e(__('saas_front::home.footer_item_3_title')); ?></div>
        <ul>
          <li><a href="/webpages/easyrank_seo"><?php echo e(__('saas_front::home.footer_item_3_list_1')); ?></a></li>
          <li><a href="/webpages/product_customization"><?php echo e(__('saas_front::home.footer_item_3_list_2')); ?></a></li>
          <li><a href="/webpages/relocation_services"><?php echo e(__('saas_front::home.footer_item_3_list_3')); ?></a></li>
          <li><a href="/webpages/email_marketing"><?php echo e(__('saas_front::home.footer_item_3_list_4')); ?></a></li>
        </ul>
      </div>
      <div class="footer-list">
        <div class="footer-list-title"><?php echo e(__('saas_front::home.footer_item_4_title')); ?></div>
        <ul>
          <li><a href="/webpages/payment_business"><?php echo e(__('saas_front::home.footer_item_4_list_1')); ?></a></li>
          <li><a href="/webpages/traffic_business"><?php echo e(__('saas_front::home.footer_item_4_list_2')); ?></a></li>
          <li><a href="/webpages/logistics"><?php echo e(__('saas_front::home.footer_item_4_list_3')); ?></a></li>
        </ul>
      </div>
    </div>
    <p class="footer-links">
      <a target="_blank" href="/webpages/user_agreement"><?php echo e(__('saas_front::home.footer_user_agreement')); ?> | </a>
      <a target="_blank" href="/webpages/privacy_policy"><?php echo e(__('saas_front::home.footer_privacy')); ?> | </a>
      
      <a target="_blank" href="/webpages/compliance_center"><?php echo e(__('saas_front::home.footer_compliance')); ?></a>
    </p>

    

    <div class="copyright-wrap">
      <a href="">&copy; <?php echo e(__('saas_front::home.footer_copyright')); ?></a>
    </div>
  </div>

  <div class="to-top sidebar-bottom">
    <a>
      <img src="https://shoplineapp.cn/images/arrow_backtotop.webp" class="img-fluid">
    </a>
  </div>
</footer>

<?php $__env->startPush('footer'); ?>
  <script>
    // 判断 footer 的 offsetTop 是否小于 屏幕高度
    const footerOffsetTop = $('footer').offset().top + $('footer').height();
    if (footerOffsetTop < $(window).height()) {
      $('.page-content').css('min-height', $(window).height() - $('footer').outerHeight(true) - $('header').outerHeight(true));
    }

    $(document).on("click", ".to-top", function (e) {
      $('html, body').animate({ 'scrollTop': 0 }, 200);
    });

    $(window).scroll(function () {
      if ($(window).scrollTop() > 120) {
        $('.to-top').removeClass('sidebar-bottom')
      } else {
        $('.to-top').addClass('sidebar-bottom')
      }
    })

    $('.question-wrap').click(function () {
      $(this).find('.second-img').each(function () {
        if ($(this).hasClass('animal-img')) {
          console.log(1)
          $(this).removeClass('animal-img')
        } else {
          console.log(2)
          $(this).addClass('animal-img')
        }
      })
    })

    $('.footer-wrap a').each(function() {
      if (!$(this).attr('href')) {
        $(this).click(function (e) {
          e.preventDefault();
          layer.msg('<?php echo e(__('saas_front::home.text8')); ?>', {
            time: 1500, 
          });
        });
      }
    });

  </script>
<?php $__env->stopPush(); ?>
<?php /**PATH D:\shopleadeCont\git\saas\beike\SaasFront\Providers/../Views/layout/footer.blade.php ENDPATH**/ ?>