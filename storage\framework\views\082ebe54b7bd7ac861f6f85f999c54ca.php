<?php
    $sidebar_menu = [
        [
            'name' => trans('saas_front::account.store_management'),
            'icon' => '&#xe735;',
            'route' => route('front.account.stores.index'),
            'active' => equal_route(['front.account.stores.index', 'front.account.stores.create']),
        ],
        [
            'name' => trans('saas_front::account.order_management'),
            'icon' => '&#xe625;',
            'route' => route('front.account.bills.index'),
            'active' => equal_route(['front.account.bills.index']),
        ],
        [
            'name' => trans('saas_front::issue.list_title_1'),
            'icon' => '&#xe62e;',
            'route' => route('front.account.issues.index'),
            'active' => equal_route([
                'front.account.issues.index',
                'front.account.issues.create',
                'front.account.issues.show',
            ]),
        ],
        [
            'name' => trans('saas_front::account.data_management'),
            'icon' => '&#xe8c6;',
            'route' => route('front.account.data.index'),
            'active' => equal_route(['front.account.data.index']),
        ],
        [
            'name' => trans('saas_front::account.tuiguang_dashi'),
            'icon' => '&#xe602;',
            'route' => route('front.account.promo.index'),
            'active' => equal_route(['front.account.promo.index']),
        ],
        [
            'name' => trans('saas_front::account.ab_edit'),
            'icon' => '&#xe603;',
            'route' => route('front.account.account_abtest.index'),
            'active' => equal_route(['front.account.account_abtest.index']),
        ],
    ];
?>

<?php $__env->startPush('header'); ?>
    <script src="<?php echo e(asset('vendor/scrolltofixed/jquery-scrolltofixed-min.js')); ?>"></script>
<?php $__env->stopPush(); ?>

<div class="account-sidebar x-fixed-top" id="accountSidebarLeft">
    
</div>

<?php $__env->startPush('footer'); ?>
    <script>
        let userinfo = null;
        const nav_data = <?php echo json_encode($sidebar_menu, 15, 512) ?>;
        async function getUserInfo() {
        const tuiguang = <?php echo json_encode(trans('saas_front::account.tuiguang_dashi'), 15, 512) ?>;
        const ab = <?php echo json_encode(trans('saas_front::account.ab_edit'), 15, 512) ?>;
            await $http.get('/account/info').then(res => {
                userinfo = res.data.info;
                let str = `
        <ul style="overflow-x: auto;">
          ${nav_data.map(item => {
            if (item.name === <?php echo json_encode(trans('saas_front::account.tuiguang_dashi'), 15, 512) ?>) {
              if (userinfo.is_invite === 1) {
                return `
                        <li>
                          <a href="${item.route}" class="${item.active ? 'active' : ''}">
                            <i class="${item.name === tuiguang ? 'login_iconfont' : 'iconfont'}">${item.icon}</i>
                            <span style="text-align: center;">${item.name}</span>
                          </a>
                        </li>
                        `
              } else {
                return ''
              }
            } else if(item.name === <?php echo json_encode(trans('saas_front::account.ab_edit'), 15, 512) ?>){
              if (userinfo.ab_status === 1) {
                return `
                        <li>
                          <a href="${item.route}" class="${item.active ? 'active' : ''}">
                            <i class="${item.name === ab ? 'login_iconfont' : 'iconfont'}">${item.icon}</i>
                            <span style="text-align: center;">${item.name}</span>
                          </a>
                        </li>
                        `
              } else {
                return ''
              }
            } else {
              return ` 
              <li>
                <a href = "${item.route}"  class = "${item.active ? 'active' : ''}" >
                  <i class ="${item.name === 'saas_front::account.tuiguang_dashi' ? 'login_iconfont' : 'iconfont'}"> ${item.icon}</i> 
                  <span style="text-align: center;">${item.name}</span>
                </a>
              </li>
                `
            }
          }).join('')}
        </ul>
        `
         $('#accountSidebarLeft').html(str);
            })
        }
        getUserInfo()
    </script>
    <script>
        if ($(window).width() <= 768) {
            $('.table').each((index, table) => {
                if (!$(table).hasClass('table-no-mb')) {
                    $(table).addClass('table-mb')
                    $(table).find('tbody tr').each((hi, tr) => {
                        $(tr).find('td').each((ti, td) => {
                            let th = $(td).closest('table').find('thead th').eq(ti);
                            if (th.length) {
                                if (th.html().indexOf('checkbox') === -1) {
                                    $(td).attr('data-label', th.text())
                                } else {
                                    $(td).attr('data-label', lang.choose)
                                }
                            } else {
                                $(td).closest('table').removeClass('table-mb')
                            }
                        })
                    })
                }
            })
        }
    </script>
<?php $__env->stopPush(); ?>
<?php /**PATH D:\shopleadeCont\git\saas\beike\SaasFront\Providers/../Views/pages/account/shared/menu.blade.php ENDPATH**/ ?>