{"__meta": {"id": "01K4P1C30T2YWWRQXHYF2NARJW", "datetime": "2025-09-09 09:27:46", "utime": **********.45854, "method": "GET", "uri": "/latest_products", "ip": "127.0.0.1"}, "php": {"version": "8.2.9", "interface": "cgi-fcgi"}, "messages": {"count": 41, "messages": [{"message": "HOOK === hook_filter: footer.content", "message_html": null, "is_string": true, "label": "log", "time": **********.174892, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === hook_filter: model.page.url", "message_html": null, "is_string": true, "label": "log", "time": **********.329732, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === hook_filter: model.page.url", "message_html": null, "is_string": true, "label": "log", "time": **********.32992, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === hook_filter: model.page.url", "message_html": null, "is_string": true, "label": "log", "time": **********.482273, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === hook_filter: model.page.url", "message_html": null, "is_string": true, "label": "log", "time": **********.482594, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === hook_filter: menu.content", "message_html": null, "is_string": true, "label": "log", "time": **********.48284, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === hook_filter: repo.product.builder", "message_html": null, "is_string": true, "label": "log", "time": **********.486603, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === hook_filter: model.product.url", "message_html": null, "is_string": true, "label": "log", "time": **********.130777, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === hook_filter: resource.product.simple", "message_html": null, "is_string": true, "label": "log", "time": **********.327944, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === hook_filter: model.product.url", "message_html": null, "is_string": true, "label": "log", "time": **********.531682, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === hook_filter: resource.product.simple", "message_html": null, "is_string": true, "label": "log", "time": **********.53202, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === hook_filter: model.product.url", "message_html": null, "is_string": true, "label": "log", "time": **********.532211, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === hook_filter: resource.product.simple", "message_html": null, "is_string": true, "label": "log", "time": **********.532428, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hook: product_list.item.image.tag", "message_html": null, "is_string": true, "label": "log", "time": **********.541409, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hookwrapper: shared.product.btn.add_cart", "message_html": null, "is_string": true, "label": "log", "time": **********.543549, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hook: product_list.item.name.before", "message_html": null, "is_string": true, "label": "log", "time": **********.543877, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hook: product_list.item.after", "message_html": null, "is_string": true, "label": "log", "time": **********.54437, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hook: product_list.item.image.tag", "message_html": null, "is_string": true, "label": "log", "time": **********.54578, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hookwrapper: shared.product.btn.add_cart", "message_html": null, "is_string": true, "label": "log", "time": **********.546192, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hook: product_list.item.name.before", "message_html": null, "is_string": true, "label": "log", "time": **********.546224, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hook: product_list.item.after", "message_html": null, "is_string": true, "label": "log", "time": **********.546256, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hook: product_list.item.image.tag", "message_html": null, "is_string": true, "label": "log", "time": **********.54652, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hookwrapper: shared.product.btn.add_cart", "message_html": null, "is_string": true, "label": "log", "time": **********.546709, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hook: product_list.item.name.before", "message_html": null, "is_string": true, "label": "log", "time": **********.546738, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hook: product_list.item.after", "message_html": null, "is_string": true, "label": "log", "time": **********.546783, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hook: layout.header.code", "message_html": null, "is_string": true, "label": "log", "time": **********.550293, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hookwrapper: header.top.currency", "message_html": null, "is_string": true, "label": "log", "time": **********.892673, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hookwrapper: header.top.language", "message_html": null, "is_string": true, "label": "log", "time": **********.145231, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hookwrapper: header.top.telephone", "message_html": null, "is_string": true, "label": "log", "time": **********.145358, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hookwrapper: header.menu.logo", "message_html": null, "is_string": true, "label": "log", "time": **********.145467, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hookwrapper: header.menu.icon", "message_html": null, "is_string": true, "label": "log", "time": **********.30208, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hook: header.menu.before", "message_html": null, "is_string": true, "label": "log", "time": **********.31237, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hook: header.menu.after", "message_html": null, "is_string": true, "label": "log", "time": **********.312464, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hook: footer.before", "message_html": null, "is_string": true, "label": "log", "time": **********.313433, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hook: footer.services.before", "message_html": null, "is_string": true, "label": "log", "time": **********.313458, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hook: footer.services.after", "message_html": null, "is_string": true, "label": "log", "time": **********.314065, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hook: footer.contact.before", "message_html": null, "is_string": true, "label": "log", "time": **********.314242, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hookwrapper: footer.contact", "message_html": null, "is_string": true, "label": "log", "time": **********.314295, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hook: footer.contact.after", "message_html": null, "is_string": true, "label": "log", "time": **********.31431, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hookwrapper: footer.copyright", "message_html": null, "is_string": true, "label": "log", "time": **********.314348, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hook: footer.after", "message_html": null, "is_string": true, "label": "log", "time": **********.314362, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}]}, "time": {"start": 1757381259.309402, "end": **********.458572, "duration": 7.149169921875, "duration_str": "7.15s", "measures": [{"label": "Booting", "start": 1757381259.309402, "relative_start": 0, "end": **********.283778, "relative_end": **********.283778, "duration": 1.****************, "duration_str": "1.97s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.283787, "relative_start": 1.****************, "end": **********.458573, "relative_end": 1.1920928955078125e-06, "duration": 5.**************, "duration_str": "5.17s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.289557, "relative_start": 1.****************, "end": **********.295384, "relative_end": **********.295384, "duration": 0.0058269500732421875, "duration_str": "5.83ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.534197, "relative_start": 6.**************, "end": **********.456139, "relative_end": **********.456139, "duration": 0.****************, "duration_str": "922ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.457088, "relative_start": 7.***************, "end": **********.457131, "relative_end": **********.457131, "duration": 4.291534423828125e-05, "duration_str": "43μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 16, "nb_templates": 16, "templates": [{"name": "1x LatestProducts::shop.latest_products", "param_count": null, "params": [], "start": **********.535678, "type": "blade", "hash": "bladeD:\\shopleadeCont\\git\\saas\\plugins/LatestProducts/Views/shop/latest_products.blade.phpLatestProducts::shop.latest_products", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fplugins%2FLatestProducts%2FViews%2Fshop%2Flatest_products.blade.php&line=1", "ajax": false, "filename": "latest_products.blade.php", "line": "?"}, "render_count": 1, "name_original": "LatestProducts::shop.latest_products"}, {"name": "3x shared.product", "param_count": null, "params": [], "start": **********.538037, "type": "blade", "hash": "bladeD:\\shopleadeCont\\git\\saas\\themes\\default/shared/product.blade.phpshared.product", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fthemes%2Fdefault%2Fshared%2Fproduct.blade.php&line=1", "ajax": false, "filename": "product.blade.php", "line": "?"}, "render_count": 3, "name_original": "shared.product"}, {"name": "3x ProductSpecial::shop.product_list_image_tag", "param_count": null, "params": [], "start": **********.541763, "type": "blade", "hash": "bladeD:\\shopleadeCont\\git\\saas\\plugins/ProductSpecial/Views/shop/product_list_image_tag.blade.phpProductSpecial::shop.product_list_image_tag", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fplugins%2FProductSpecial%2FViews%2Fshop%2Fproduct_list_image_tag.blade.php&line=1", "ajax": false, "filename": "product_list_image_tag.blade.php", "line": "?"}, "render_count": 3, "name_original": "ProductSpecial::shop.product_list_image_tag"}, {"name": "1x shared.pagination.bootstrap-4", "param_count": null, "params": [], "start": **********.548052, "type": "blade", "hash": "bladeD:\\shopleadeCont\\git\\saas\\themes\\default/shared/pagination/bootstrap-4.blade.phpshared.pagination.bootstrap-4", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fthemes%2Fdefault%2Fshared%2Fpagination%2Fbootstrap-4.blade.php&line=1", "ajax": false, "filename": "bootstrap-4.blade.php", "line": "?"}, "render_count": 1, "name_original": "shared.pagination.bootstrap-4"}, {"name": "1x layout.master", "param_count": null, "params": [], "start": **********.549888, "type": "blade", "hash": "bladeD:\\shopleadeCont\\git\\saas\\themes\\fashion/layout/master.blade.phplayout.master", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fthemes%2Ffashion%2Flayout%2Fmaster.blade.php&line=1", "ajax": false, "filename": "master.blade.php", "line": "?"}, "render_count": 1, "name_original": "layout.master"}, {"name": "1x GoogleAnalytics::layout_header_code", "param_count": null, "params": [], "start": **********.550465, "type": "blade", "hash": "bladeD:\\shopleadeCont\\git\\saas\\plugins/GoogleAnalytics/Views/layout_header_code.blade.phpGoogleAnalytics::layout_header_code", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fplugins%2FGoogleAnalytics%2FViews%2Flayout_header_code.blade.php&line=1", "ajax": false, "filename": "layout_header_code.blade.php", "line": "?"}, "render_count": 1, "name_original": "GoogleAnalytics::layout_header_code"}, {"name": "1x TikTokPixel::layout_header_code", "param_count": null, "params": [], "start": **********.890855, "type": "blade", "hash": "bladeD:\\shopleadeCont\\git\\saas\\plugins/TikTokPixel/Views/layout_header_code.blade.phpTikTokPixel::layout_header_code", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fplugins%2FTikTokPixel%2FViews%2Flayout_header_code.blade.php&line=1", "ajax": false, "filename": "layout_header_code.blade.php", "line": "?"}, "render_count": 1, "name_original": "TikTokPixel::layout_header_code"}, {"name": "1x SaleSmartly::layout_header_code", "param_count": null, "params": [], "start": **********.891259, "type": "blade", "hash": "bladeD:\\shopleadeCont\\git\\saas\\plugins/SaleSmartly/Views/layout_header_code.blade.phpSaleSmartly::layout_header_code", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fplugins%2FSaleSmartly%2FViews%2Flayout_header_code.blade.php&line=1", "ajax": false, "filename": "layout_header_code.blade.php", "line": "?"}, "render_count": 1, "name_original": "SaleSmartly::layout_header_code"}, {"name": "1x TwitterPixel::layout_header_code", "param_count": null, "params": [], "start": **********.891557, "type": "blade", "hash": "bladeD:\\shopleadeCont\\git\\saas\\plugins/TwitterPixel/Views/layout_header_code.blade.phpTwitterPixel::layout_header_code", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fplugins%2FTwitterPixel%2FViews%2Flayout_header_code.blade.php&line=1", "ajax": false, "filename": "layout_header_code.blade.php", "line": "?"}, "render_count": 1, "name_original": "TwitterPixel::layout_header_code"}, {"name": "1x layout.header", "param_count": null, "params": [], "start": **********.891982, "type": "blade", "hash": "bladeD:\\shopleadeCont\\git\\saas\\themes\\fashion/layout/header.blade.phplayout.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fthemes%2Ffashion%2Flayout%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "layout.header"}, {"name": "1x shared.menu-pc", "param_count": null, "params": [], "start": **********.311744, "type": "blade", "hash": "bladeD:\\shopleadeCont\\git\\saas\\themes\\fashion/shared/menu-pc.blade.phpshared.menu-pc", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fthemes%2Ffashion%2Fshared%2Fmenu-pc.blade.php&line=1", "ajax": false, "filename": "menu-pc.blade.php", "line": "?"}, "render_count": 1, "name_original": "shared.menu-pc"}, {"name": "1x layout.footer", "param_count": null, "params": [], "start": **********.313029, "type": "blade", "hash": "bladeD:\\shopleadeCont\\git\\saas\\themes\\fashion/layout/footer.blade.phplayout.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fthemes%2Ffashion%2Flayout%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "layout.footer"}]}, "route": {"uri": "GET latest_products", "middleware": "shop", "controller": "Plugin\\LatestProducts\\Controllers\\MenusController@latestProducts<a href=\"phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fplugins%2FLatestProducts%2FControllers%2FMenusController.php&line=28\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "shop.latest_products", "file": "<a href=\"phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fplugins%2FLatestProducts%2FControllers%2FMenusController.php&line=28\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">plugins/LatestProducts/Controllers/MenusController.php:28-47</a>"}, "queries": {"count": 28, "nb_statements": 28, "nb_visible_statements": 28, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 5.05731, "accumulated_duration_str": "5.06s", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select count(*) as aggregate from `saas_commissions` where `saas_store_id` = 88 and `status` = 1 and `total` >= '0.01' and `paid_at` is null", "type": "query", "params": [], "bindings": [88, 1, "0.01"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/CheckStoreExpired.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\CheckStoreExpired.php", "line": 29}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 119}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 805}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 784}], "start": **********.299181, "duration": 0.*****************, "duration_str": "244ms", "memory": 0, "memory_str": null, "filename": "CheckStoreExpired.php:29", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/CheckStoreExpired.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\CheckStoreExpired.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fapp%2FHttp%2FMiddleware%2FCheckStoreExpired.php&line=29", "ajax": false, "filename": "CheckStoreExpired.php", "line": "29"}, "connection": "wintoshop", "explain": null, "start_percent": 0, "width_percent": 4.832}, {"sql": "select * from `languages` where `status` = 1 and `languages`.`store_id` = 88 order by `sort_order` asc", "type": "query", "params": [], "bindings": [1, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "beike/Repositories/LanguageRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\LanguageRepo.php", "line": 101}, {"index": 16, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 491}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 121}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 64}], "start": **********.548983, "duration": 0.2013, "duration_str": "201ms", "memory": 0, "memory_str": null, "filename": "LanguageRepo.php:101", "source": {"index": 15, "namespace": null, "name": "beike/Repositories/LanguageRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\LanguageRepo.php", "line": 101}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FRepositories%2FLanguageRepo.php&line=101", "ajax": false, "filename": "LanguageRepo.php", "line": "101"}, "connection": "wintoshop", "explain": null, "start_percent": 4.832, "width_percent": 3.98}, {"sql": "select * from `languages` where `status` = 1 and `languages`.`store_id` = 88 order by `sort_order` asc", "type": "query", "params": [], "bindings": [1, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "beike/Repositories/LanguageRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\LanguageRepo.php", "line": 101}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 45}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 25}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 19, "namespace": null, "name": "app/Http/Middleware/SetLocaleFromSession.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\SetLocaleFromSession.php", "line": 37}], "start": **********.752699, "duration": 0.2165, "duration_str": "217ms", "memory": 0, "memory_str": null, "filename": "LanguageRepo.php:101", "source": {"index": 15, "namespace": null, "name": "beike/Repositories/LanguageRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\LanguageRepo.php", "line": 101}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FRepositories%2FLanguageRepo.php&line=101", "ajax": false, "filename": "LanguageRepo.php", "line": "101"}, "connection": "wintoshop", "explain": null, "start_percent": 8.812, "width_percent": 4.281}, {"sql": "select * from `pages` where `active` = 1 and `pages`.`id` = 21 and `pages`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [1, 21, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 157}, {"index": 24, "namespace": null, "name": "beike/Repositories/FooterRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\FooterRepo.php", "line": 31}, {"index": 25, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 47}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 25}], "start": **********.9707308, "duration": 0.17915999999999999, "duration_str": "179ms", "memory": 0, "memory_str": null, "filename": "Url.php:68", "source": {"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FLibraries%2FUrl.php&line=68", "ajax": false, "filename": "Url.php", "line": "68"}, "connection": "wintoshop", "explain": null, "start_percent": 13.093, "width_percent": 3.543}, {"sql": "select * from `pages` where `active` = 1 and `pages`.`id` = 18 and `pages`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [1, 18, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 157}, {"index": 24, "namespace": null, "name": "beike/Repositories/FooterRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\FooterRepo.php", "line": 31}, {"index": 25, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 47}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 25}], "start": 1757381262.150727, "duration": 0.20423, "duration_str": "204ms", "memory": 0, "memory_str": null, "filename": "Url.php:68", "source": {"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FLibraries%2FUrl.php&line=68", "ajax": false, "filename": "Url.php", "line": "68"}, "connection": "wintoshop", "explain": null, "start_percent": 16.636, "width_percent": 4.038}, {"sql": "select `id` from `pages` where `pages`.`store_id` = 88 order by `id` desc", "type": "query", "params": [], "bindings": [88], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "beike/Repositories/PageRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\PageRepo.php", "line": 96}, {"index": 16, "namespace": null, "name": "beike/Repositories/PageRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\PageRepo.php", "line": 80}, {"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 123}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 170}, {"index": 24, "namespace": null, "name": "beike/Repositories/FooterRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\FooterRepo.php", "line": 31}], "start": 1757381262.355969, "duration": 0.15405000000000002, "duration_str": "154ms", "memory": 0, "memory_str": null, "filename": "PageRepo.php:96", "source": {"index": 15, "namespace": null, "name": "beike/Repositories/PageRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\PageRepo.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FRepositories%2FPageRepo.php&line=96", "ajax": false, "filename": "PageRepo.php", "line": "96"}, "connection": "wintoshop", "explain": null, "start_percent": 20.674, "width_percent": 3.046}, {"sql": "select * from `page_descriptions` where `locale` = 'zh_cn' and `page_descriptions`.`page_id` in (10, 11)", "type": "query", "params": [], "bindings": ["zh_cn"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "beike/Repositories/PageRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\PageRepo.php", "line": 96}, {"index": 21, "namespace": null, "name": "beike/Repositories/PageRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\PageRepo.php", "line": 80}, {"index": 22, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 123}, {"index": 23, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 170}, {"index": 29, "namespace": null, "name": "beike/Repositories/FooterRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\FooterRepo.php", "line": 31}], "start": 1757381262.514636, "duration": 0.25045, "duration_str": "250ms", "memory": 0, "memory_str": null, "filename": "PageRepo.php:96", "source": {"index": 20, "namespace": null, "name": "beike/Repositories/PageRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\PageRepo.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FRepositories%2FPageRepo.php&line=96", "ajax": false, "filename": "PageRepo.php", "line": "96"}, "connection": "wintoshop", "explain": null, "start_percent": 23.72, "width_percent": 4.952}, {"sql": "select * from `pages` where `active` = 1 and `pages`.`id` = 12 and `pages`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [1, 12, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 157}, {"index": 24, "namespace": null, "name": "beike/Repositories/FooterRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\FooterRepo.php", "line": 31}, {"index": 25, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 47}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 25}], "start": 1757381262.7658901, "duration": 0.10461, "duration_str": "105ms", "memory": 0, "memory_str": null, "filename": "Url.php:68", "source": {"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FLibraries%2FUrl.php&line=68", "ajax": false, "filename": "Url.php", "line": "68"}, "connection": "wintoshop", "explain": null, "start_percent": 28.672, "width_percent": 2.068}, {"sql": "select * from `pages` where `active` = 1 and `pages`.`id` = 20 and `pages`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [1, 20, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 157}, {"index": 24, "namespace": null, "name": "beike/Repositories/FooterRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\FooterRepo.php", "line": 31}, {"index": 25, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 47}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 25}], "start": 1757381262.8729901, "duration": 0.14921, "duration_str": "149ms", "memory": 0, "memory_str": null, "filename": "Url.php:68", "source": {"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FLibraries%2FUrl.php&line=68", "ajax": false, "filename": "Url.php", "line": "68"}, "connection": "wintoshop", "explain": null, "start_percent": 30.741, "width_percent": 2.95}, {"sql": "select * from `pages` where `active` = 1 and `pages`.`id` = 21 and `pages`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [1, 21, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 157}, {"index": 24, "namespace": null, "name": "beike/Repositories/FooterRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\FooterRepo.php", "line": 31}, {"index": 25, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 47}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 25}], "start": **********.022836, "duration": 0.15147, "duration_str": "151ms", "memory": 0, "memory_str": null, "filename": "Url.php:68", "source": {"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FLibraries%2FUrl.php&line=68", "ajax": false, "filename": "Url.php", "line": "68"}, "connection": "wintoshop", "explain": null, "start_percent": 33.691, "width_percent": 2.995}, {"sql": "select * from `pages` where `active` = 1 and `pages`.`id` = 11 and `pages`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [1, 11, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 157}, {"index": 21, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 48}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 25}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.175606, "duration": 0.15359, "duration_str": "154ms", "memory": 0, "memory_str": null, "filename": "Url.php:68", "source": {"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FLibraries%2FUrl.php&line=68", "ajax": false, "filename": "Url.php", "line": "68"}, "connection": "wintoshop", "explain": null, "start_percent": 36.686, "width_percent": 3.037}, {"sql": "select * from `pages` where `active` = 1 and `pages`.`id` = 10 and `pages`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [1, 10, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 157}, {"index": 21, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 48}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 25}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.330291, "duration": 0.15112, "duration_str": "151ms", "memory": 0, "memory_str": null, "filename": "Url.php:68", "source": {"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FLibraries%2FUrl.php&line=68", "ajax": false, "filename": "Url.php", "line": "68"}, "connection": "wintoshop", "explain": null, "start_percent": 39.723, "width_percent": 2.988}, {"sql": "select count(*) as aggregate from `products` left join `product_descriptions` as `pd` on `pd`.`product_id` = `products`.`id` and `locale` = 'zh_cn' where `active` = 1 and exists (select * from `product_skus` where `products`.`id` = `product_skus`.`product_id` and `is_default` = 1 and `product_skus`.`store_id` = 88) and `products`.`deleted_at` is null and `products`.`store_id` = 88", "type": "query", "params": [], "bindings": ["zh_cn", 1, 1, 88, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "plugins/LatestProducts/Controllers/MenusController.php", "file": "D:\\shopleadeCont\\git\\saas\\plugins\\LatestProducts\\Controllers\\MenusController.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.48788, "duration": 0.*****************, "duration_str": "199ms", "memory": 0, "memory_str": null, "filename": "MenusController.php:39", "source": {"index": 16, "namespace": null, "name": "plugins/LatestProducts/Controllers/MenusController.php", "file": "D:\\shopleadeCont\\git\\saas\\plugins\\LatestProducts\\Controllers\\MenusController.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fplugins%2FLatestProducts%2FControllers%2FMenusController.php&line=39", "ajax": false, "filename": "MenusController.php", "line": "39"}, "connection": "wintoshop", "explain": null, "start_percent": 42.711, "width_percent": 3.931}, {"sql": "select `products`.*, `pd`.`name`, `pd`.`content`, `pd`.`meta_title`, `pd`.`meta_description`, `pd`.`meta_keywords`, `pd`.`name` from `products` left join `product_descriptions` as `pd` on `pd`.`product_id` = `products`.`id` and `locale` = 'zh_cn' where `active` = 1 and exists (select * from `product_skus` where `products`.`id` = `product_skus`.`product_id` and `is_default` = 1 and `product_skus`.`store_id` = 88) and `products`.`deleted_at` is null and `products`.`store_id` = 88 order by `products`.`created_at` desc, `created_at` desc limit 20 offset 0", "type": "query", "params": [], "bindings": ["zh_cn", 1, 1, 88, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "plugins/LatestProducts/Controllers/MenusController.php", "file": "D:\\shopleadeCont\\git\\saas\\plugins\\LatestProducts\\Controllers\\MenusController.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.6882029, "duration": 0.*****************, "duration_str": "155ms", "memory": 0, "memory_str": null, "filename": "MenusController.php:39", "source": {"index": 16, "namespace": null, "name": "plugins/LatestProducts/Controllers/MenusController.php", "file": "D:\\shopleadeCont\\git\\saas\\plugins\\LatestProducts\\Controllers\\MenusController.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fplugins%2FLatestProducts%2FControllers%2FMenusController.php&line=39", "ajax": false, "filename": "MenusController.php", "line": "39"}, "connection": "wintoshop", "explain": null, "start_percent": 46.642, "width_percent": 3.059}, {"sql": "select * from `product_descriptions` where `locale` = 'zh_cn' and `product_descriptions`.`product_id` in (100191, 100217, 100220)", "type": "query", "params": [], "bindings": ["zh_cn"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "plugins/LatestProducts/Controllers/MenusController.php", "file": "D:\\shopleadeCont\\git\\saas\\plugins\\LatestProducts\\Controllers\\MenusController.php", "line": 39}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.8445091, "duration": 0.25098, "duration_str": "251ms", "memory": 0, "memory_str": null, "filename": "MenusController.php:39", "source": {"index": 21, "namespace": null, "name": "plugins/LatestProducts/Controllers/MenusController.php", "file": "D:\\shopleadeCont\\git\\saas\\plugins\\LatestProducts\\Controllers\\MenusController.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fplugins%2FLatestProducts%2FControllers%2FMenusController.php&line=39", "ajax": false, "filename": "MenusController.php", "line": "39"}, "connection": "wintoshop", "explain": null, "start_percent": 49.702, "width_percent": 4.963}, {"sql": "select * from `product_skus` where `product_skus`.`product_id` in (100191, 100217, 100220) and `product_skus`.`store_id` = 88", "type": "query", "params": [], "bindings": [88], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "plugins/LatestProducts/Controllers/MenusController.php", "file": "D:\\shopleadeCont\\git\\saas\\plugins\\LatestProducts\\Controllers\\MenusController.php", "line": 39}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.098101, "duration": 0.*****************, "duration_str": "152ms", "memory": 0, "memory_str": null, "filename": "MenusController.php:39", "source": {"index": 21, "namespace": null, "name": "plugins/LatestProducts/Controllers/MenusController.php", "file": "D:\\shopleadeCont\\git\\saas\\plugins\\LatestProducts\\Controllers\\MenusController.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fplugins%2FLatestProducts%2FControllers%2FMenusController.php&line=39", "ajax": false, "filename": "MenusController.php", "line": "39"}, "connection": "wintoshop", "explain": null, "start_percent": 54.664, "width_percent": 3.007}, {"sql": "select * from `product_skus` where `is_default` = 1 and `product_skus`.`product_id` in (100191, 100217, 100220) and `product_skus`.`store_id` = 88", "type": "query", "params": [], "bindings": [1, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "plugins/LatestProducts/Controllers/MenusController.php", "file": "D:\\shopleadeCont\\git\\saas\\plugins\\LatestProducts\\Controllers\\MenusController.php", "line": 39}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.251179, "duration": 0.*****************, "duration_str": "151ms", "memory": 0, "memory_str": null, "filename": "MenusController.php:39", "source": {"index": 21, "namespace": null, "name": "plugins/LatestProducts/Controllers/MenusController.php", "file": "D:\\shopleadeCont\\git\\saas\\plugins\\LatestProducts\\Controllers\\MenusController.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fplugins%2FLatestProducts%2FControllers%2FMenusController.php&line=39", "ajax": false, "filename": "MenusController.php", "line": "39"}, "connection": "wintoshop", "explain": null, "start_percent": 57.671, "width_percent": 2.988}, {"sql": "select * from `product_attributes` where `product_attributes`.`product_id` in (100191, 100217, 100220)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "plugins/LatestProducts/Controllers/MenusController.php", "file": "D:\\shopleadeCont\\git\\saas\\plugins\\LatestProducts\\Controllers\\MenusController.php", "line": 39}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.40485, "duration": 0.*****************, "duration_str": "155ms", "memory": 0, "memory_str": null, "filename": "MenusController.php:39", "source": {"index": 21, "namespace": null, "name": "plugins/LatestProducts/Controllers/MenusController.php", "file": "D:\\shopleadeCont\\git\\saas\\plugins\\LatestProducts\\Controllers\\MenusController.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fplugins%2FLatestProducts%2FControllers%2FMenusController.php&line=39", "ajax": false, "filename": "MenusController.php", "line": "39"}, "connection": "wintoshop", "explain": null, "start_percent": 60.659, "width_percent": 3.067}, {"sql": "select * from `brands` where `brands`.`id` in (0) and `brands`.`store_id` = 88", "type": "query", "params": [], "bindings": [88], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "plugins/LatestProducts/Controllers/MenusController.php", "file": "D:\\shopleadeCont\\git\\saas\\plugins\\LatestProducts\\Controllers\\MenusController.php", "line": 39}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.563013, "duration": 0.25438, "duration_str": "254ms", "memory": 0, "memory_str": null, "filename": "MenusController.php:39", "source": {"index": 21, "namespace": null, "name": "plugins/LatestProducts/Controllers/MenusController.php", "file": "D:\\shopleadeCont\\git\\saas\\plugins\\LatestProducts\\Controllers\\MenusController.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fplugins%2FLatestProducts%2FControllers%2FMenusController.php&line=39", "ajax": false, "filename": "MenusController.php", "line": "39"}, "connection": "wintoshop", "explain": null, "start_percent": 63.726, "width_percent": 5.03}, {"sql": "select * from `customers` where `id` = 63 and `customers`.`deleted_at` is null and `customers`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [63, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 265}, {"index": 27, "namespace": null, "name": "plugins/LatestProducts/Controllers/MenusController.php", "file": "D:\\shopleadeCont\\git\\saas\\plugins\\LatestProducts\\Controllers\\MenusController.php", "line": 39}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.819847, "duration": 0.15025, "duration_str": "150ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "wintoshop", "explain": null, "start_percent": 68.756, "width_percent": 2.971}, {"sql": "select * from `customer_wishlists` where `customer_id` = 0 and `customer_wishlists`.`product_id` in (100191, 100217, 100220)", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "plugins/LatestProducts/Controllers/MenusController.php", "file": "D:\\shopleadeCont\\git\\saas\\plugins\\LatestProducts\\Controllers\\MenusController.php", "line": 39}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.971162, "duration": 0.*****************, "duration_str": "155ms", "memory": 0, "memory_str": null, "filename": "MenusController.php:39", "source": {"index": 21, "namespace": null, "name": "plugins/LatestProducts/Controllers/MenusController.php", "file": "D:\\shopleadeCont\\git\\saas\\plugins\\LatestProducts\\Controllers\\MenusController.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fplugins%2FLatestProducts%2FControllers%2FMenusController.php&line=39", "ajax": false, "filename": "MenusController.php", "line": "39"}, "connection": "wintoshop", "explain": null, "start_percent": 71.727, "width_percent": 3.06}, {"sql": "select * from `currencies` where `status` = 1 and `currencies`.`store_id` = 88", "type": "query", "params": [], "bindings": [1, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "beike/Repositories/CurrencyRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\CurrencyRepo.php", "line": 91}, {"index": 16, "namespace": null, "name": "beike/Services/CurrencyService.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Services\\CurrencyService.php", "line": 25}, {"index": 17, "namespace": null, "name": "beike/Services/CurrencyService.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Services\\CurrencyService.php", "line": 33}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 358}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}], "start": **********.131634, "duration": 0.19303, "duration_str": "193ms", "memory": 0, "memory_str": null, "filename": "CurrencyRepo.php:91", "source": {"index": 15, "namespace": null, "name": "beike/Repositories/CurrencyRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\CurrencyRepo.php", "line": 91}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FRepositories%2FCurrencyRepo.php&line=91", "ajax": false, "filename": "CurrencyRepo.php", "line": "91"}, "connection": "wintoshop", "explain": null, "start_percent": 74.787, "width_percent": 3.817}, {"sql": "select * from `product_specials` where `date_start` < '2025-09-09 09:27:45' and `date_end` > '2025-09-09 09:27:45'", "type": "query", "params": [], "bindings": ["2025-09-09 09:27:45", "2025-09-09 09:27:45"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "plugins/ProductSpecial/Repositories/ProductSpecialRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\plugins\\ProductSpecial\\Repositories\\ProductSpecialRepo.php", "line": 92}, {"index": 16, "namespace": null, "name": "plugins/ProductSpecial/Bootstrap.php", "file": "D:\\shopleadeCont\\git\\saas\\plugins\\ProductSpecial\\Bootstrap.php", "line": 60}, {"index": 22, "namespace": null, "name": "vendor/tormjens/eventy/src/Filter.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\tormjens\\eventy\\src\\Filter.php", "line": 21}, {"index": 23, "namespace": null, "name": "vendor/tormjens/eventy/src/Events.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\tormjens\\eventy\\src\\Events.php", "line": 163}, {"index": 25, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 695}], "start": **********.3320599, "duration": 0.19846, "duration_str": "198ms", "memory": 0, "memory_str": null, "filename": "ProductSpecialRepo.php:92", "source": {"index": 15, "namespace": null, "name": "plugins/ProductSpecial/Repositories/ProductSpecialRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\plugins\\ProductSpecial\\Repositories\\ProductSpecialRepo.php", "line": 92}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fplugins%2FProductSpecial%2FRepositories%2FProductSpecialRepo.php&line=92", "ajax": false, "filename": "ProductSpecialRepo.php", "line": "92"}, "connection": "wintoshop", "explain": null, "start_percent": 78.604, "width_percent": 3.924}, {"sql": "select * from `customers` where `id` = 63 and `customers`.`deleted_at` is null and `customers`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [63, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 265}, {"index": 22, "namespace": null, "name": "beike/Hook/Hook.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Hook\\Hook.php", "line": 40}, {"index": 23, "namespace": null, "name": "beike/Hook/Hook.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Hook\\Hook.php", "line": 64}], "start": **********.5509079, "duration": 0.18581, "duration_str": "186ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "wintoshop", "explain": null, "start_percent": 82.528, "width_percent": 3.674}, {"sql": "select * from `customers` where `id` = 63 and `customers`.`deleted_at` is null and `customers`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [63, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 265}, {"index": 22, "namespace": null, "name": "beike/Hook/Hook.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Hook\\Hook.php", "line": 40}, {"index": 23, "namespace": null, "name": "beike/Hook/Hook.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Hook\\Hook.php", "line": 64}], "start": **********.73804, "duration": 0.15153999999999998, "duration_str": "152ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "wintoshop", "explain": null, "start_percent": 86.202, "width_percent": 2.996}, {"sql": "select * from `languages` where `code` = 'zh_cn' and `languages`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": ["zh_cn", 88], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 503}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.8929608, "duration": 0.25079, "duration_str": "251ms", "memory": 0, "memory_str": null, "filename": "Helpers.php:503", "source": {"index": 16, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 503}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FHelpers.php&line=503", "ajax": false, "filename": "Helpers.php", "line": "503"}, "connection": "wintoshop", "explain": null, "start_percent": 89.199, "width_percent": 4.959}, {"sql": "select * from `customers` where `id` = 63 and `customers`.`deleted_at` is null and `customers`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [63, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "view", "name": "layout.header", "file": "D:\\shopleadeCont\\git\\saas\\themes\\fashion/layout/header.blade.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.1461642, "duration": 0.15462, "duration_str": "155ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "wintoshop", "explain": null, "start_percent": 94.158, "width_percent": 3.057}, {"sql": "select * from `customers` where `id` = 63 and `customers`.`deleted_at` is null and `customers`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [63, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 265}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.314609, "duration": 0.14084, "duration_str": "141ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "wintoshop", "explain": null, "start_percent": 97.215, "width_percent": 2.785}]}, "models": {"data": {"Beike\\Models\\ProductSku": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FModels%2FProductSku.php&line=1", "ajax": false, "filename": "ProductSku.php", "line": "?"}}, "Beike\\Models\\Language": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "Beike\\Models\\Page": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FModels%2FPage.php&line=1", "ajax": false, "filename": "Page.php", "line": "?"}}, "Beike\\Models\\Product": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "Beike\\Models\\ProductDescription": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FModels%2FProductDescription.php&line=1", "ajax": false, "filename": "ProductDescription.php", "line": "?"}}, "Beike\\Models\\Currency": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "Beike\\Models\\PageDescription": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FModels%2FPageDescription.php&line=1", "ajax": false, "filename": "PageDescription.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "O4jG04i8Bcl9bpHOIqVGqqnZWApkEJZpBCBit84f", "locale": "zh_cn", "login_web_shop_59ba36addc2b2f9401580f014c7f58ea4e30989d": "63", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://newshop.shopleade.test/latest_products\"\n]", "url": "array:1 [\n  \"intended\" => \"http://jiuyia.shopleade.test/admin\"\n]", "histories": "array:6 [\n  0 => \"admin.theme.index\"\n  1 => \"admin.plugins.index\"\n  2 => \"admin.home.index\"\n  3 => \"admin.products.index\"\n  4 => \"admin.plugins.theme\"\n  5 => \"admin.settings.index\"\n]", "saas_front_locale": "zh_cn", "login_web_saas_user_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "page": "1", "login_web_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "74", "currency": "USD"}, "request": {"data": {"status": "200 OK", "full_url": "http://newshop.shopleade.test/latest_products", "action_name": "shop.latest_products", "controller_action": "Plugin\\LatestProducts\\Controllers\\MenusController@latestProducts", "uri": "GET latest_products", "controller": "Plugin\\LatestProducts\\Controllers\\MenusController@latestProducts<a href=\"phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fplugins%2FLatestProducts%2FControllers%2FMenusController.php&line=28\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fplugins%2FLatestProducts%2FControllers%2FMenusController.php&line=28\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">plugins/LatestProducts/Controllers/MenusController.php:28-47</a>", "middleware": "shop", "duration": "7.15s", "peak_memory": "34MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1704215982 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1704215982\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-198414913 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-198414913\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1948323860 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"898 characters\">_ss_s_uid=df387fd27bfaa61c2967bdd458dc2053; beike_version={%22current%22:%*********%22%2C%22latest%22:%221.5.6%22%2C%22release_date%22:%222025-05-08%22%2C%22has_new_version%22:true}; XSRF-TOKEN=eyJpdiI6InpLQzV3aFh6bXREdVI5QWNZb0t0Mnc9PSIsInZhbHVlIjoiRzlYc0x1Q3dDVkV5RHNyR3VaOHNKUmhXcW8zS2tTN2tWMWVhU05zcGN5SUJWTnpXc1FRWldycmUySW9SY3ZLdDZTMTdMMmR1R2ROanlvTUJiMUtNYUNTbTFJN0d4VjI4OVRGME5XRlFOM3VYMm5GRzRmNVFFVlRyZlNtcFN6dk4iLCJtYWMiOiI5NDU4MDUxMWY5M2FlZGM2OWMyNWIwM2MxYjEzNjFmZjlmNjQ4MjU3YmQ5MmVkMTllYTljYWRkMjM2M2Y5YzllIiwidGFnIjoiIn0%3D; shopleade_session=eyJpdiI6InRmai9LcVdwTG5hM1BOeE9YcllHb2c9PSIsInZhbHVlIjoiUmM5ZEpHY2JHZlpnY3JoS3dvZFMzYUNoQW1nelVZYk1GOW03c21tQ1FrbXVvVGJmMzR6QnRGK1VEbUptcWhBZHFiYUk0anE5K0NrSEdPQkFvM0RHajNPV1NHanl2N0cwTWkrekRUVFF6WnJQWVd4OWZveXZVUUVMRmpjNWVCdHUiLCJtYWMiOiJiZTE4OWUzYjM5ZWYwMGE0YzYyY2I3NDdlOTI5MmM2YzFlYjZkN2ZjMzMyMDFmODIwMTIwZjc4MTI1OWVmYmNhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">zh-CN,zh;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://newshop.shopleade.test/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">newshop.shopleade.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1948323860\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1676321259 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_ss_s_uid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>beike_version</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">O4jG04i8Bcl9bpHOIqVGqqnZWApkEJZpBCBit84f</span>\"\n  \"<span class=sf-dump-key>shopleade_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YkGz0mA2y0BMpk6lkQ8Dkf8G3sQ6bHa4Ejf6HE7d</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1676321259\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1117035056 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 09 Sep 2025 01:27:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"455 characters\">XSRF-TOKEN=eyJpdiI6IldmcTVMQnJtb2hBcnQzMGcySkw2UVE9PSIsInZhbHVlIjoidmVmNmYyMFYyRGpuWkwvUFdxZjNydTlvTVk3OURFdXVzNmN0K0doN3Q2M21jK2YrSEE3REUreEdCTFVSNE9xRlJZNWI3eElCL3dzaFEzMU9EYnN1bTVqamt1eHpQN3lvYm15N3F2ZC9OQ0MxaUh0b0pUS2FpNURra1lEUHpwc3MiLCJtYWMiOiJjZDZiMDUwM2RhNDM0ZDQ4ZGU1MTdhNTgyYzg0MjVkY2MxYzVmMzU5YmQ5MDAxODJjN2M4MTRjYTQzZTVlNjIxIiwidGFnIjoiIn0%3D; expires=Thu, 09 Oct 2025 01:27:46 GMT; Max-Age=2592000; path=/; domain=.shopleade.test; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"472 characters\">shopleade_session=eyJpdiI6ImdCb1M2eXZJM3lncDB6Z0lkRUI0Zmc9PSIsInZhbHVlIjoiTTZnOVRQK2hHaWx3ZVBjdDd6OUc2ZXNNWGg1OUVIUnBWUEdJZ29DbmJCTXVRd3VxSmxRamZ0cVFkYWRsM040R0VYYUVBejRhNTRJcDR0R2lyR3MxNkk5RFZGWnpENVRGeGdudmNkd1kzdit6bVJoNW84WmlJRlp5Wml4VHZ1Y0QiLCJtYWMiOiIwZTQ4ODNiZGZhY2Y5ODU2MGQyMDA2OWE3Yzk0OGRlODhiYjJjZGNjNTQ3ZDI3MTUxNDJjMmQ0Yjk3YTg5YTI2IiwidGFnIjoiIn0%3D; expires=Thu, 09 Oct 2025 01:27:46 GMT; Max-Age=2592000; path=/; domain=.shopleade.test; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"424 characters\">XSRF-TOKEN=eyJpdiI6IldmcTVMQnJtb2hBcnQzMGcySkw2UVE9PSIsInZhbHVlIjoidmVmNmYyMFYyRGpuWkwvUFdxZjNydTlvTVk3OURFdXVzNmN0K0doN3Q2M21jK2YrSEE3REUreEdCTFVSNE9xRlJZNWI3eElCL3dzaFEzMU9EYnN1bTVqamt1eHpQN3lvYm15N3F2ZC9OQ0MxaUh0b0pUS2FpNURra1lEUHpwc3MiLCJtYWMiOiJjZDZiMDUwM2RhNDM0ZDQ4ZGU1MTdhNTgyYzg0MjVkY2MxYzVmMzU5YmQ5MDAxODJjN2M4MTRjYTQzZTVlNjIxIiwidGFnIjoiIn0%3D; expires=Thu, 09-Oct-2025 01:27:46 GMT; domain=.shopleade.test; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">shopleade_session=eyJpdiI6ImdCb1M2eXZJM3lncDB6Z0lkRUI0Zmc9PSIsInZhbHVlIjoiTTZnOVRQK2hHaWx3ZVBjdDd6OUc2ZXNNWGg1OUVIUnBWUEdJZ29DbmJCTXVRd3VxSmxRamZ0cVFkYWRsM040R0VYYUVBejRhNTRJcDR0R2lyR3MxNkk5RFZGWnpENVRGeGdudmNkd1kzdit6bVJoNW84WmlJRlp5Wml4VHZ1Y0QiLCJtYWMiOiIwZTQ4ODNiZGZhY2Y5ODU2MGQyMDA2OWE3Yzk0OGRlODhiYjJjZGNjNTQ3ZDI3MTUxNDJjMmQ0Yjk3YTg5YTI2IiwidGFnIjoiIn0%3D; expires=Thu, 09-Oct-2025 01:27:46 GMT; domain=.shopleade.test; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1117035056\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-949296297 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">O4jG04i8Bcl9bpHOIqVGqqnZWApkEJZpBCBit84f</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"5 characters\">zh_cn</span>\"\n  \"<span class=sf-dump-key>login_web_shop_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>63</span>\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"45 characters\">http://newshop.shopleade.test/latest_products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://jiuyia.shopleade.test/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>histories</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">admin.theme.index</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"19 characters\">admin.plugins.index</span>\"\n    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">admin.home.index</span>\"\n    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"20 characters\">admin.products.index</span>\"\n    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"19 characters\">admin.plugins.theme</span>\"\n    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"20 characters\">admin.settings.index</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>saas_front_locale</span>\" => \"<span class=sf-dump-str title=\"5 characters\">zh_cn</span>\"\n  \"<span class=sf-dump-key>login_web_saas_user_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>login_web_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">USD</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-949296297\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://newshop.shopleade.test/latest_products", "action_name": "shop.latest_products", "controller_action": "Plugin\\LatestProducts\\Controllers\\MenusController@latestProducts"}, "badge": null}}