<?php
    $header_menus = system_setting('base.menu_setting.menus', []);
?>


<header class="bs-header" id="header-app">
    <div class="header-desktop">
        <div class="container d-flex justify-content-between align-items-center">
            <div class="left navbar-expand-lg">
                <h1 class="logo">
                    <a href="<?php echo e(saas_front_route('home.index')); ?>">
                        <img src="<?php echo e(asset('image/logo.png?v=100')); ?>" class="img-fluid">
                    </a>
                </h1>
                <div class="menu me-3">
                    <ul class="navbar-nav mx-auto">
                        <?php $__currentLoopData = $header_menus; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $menu): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php if($menu['name'][saas_front_locale()]): ?>
                                <li
                                    class="nav-item <?php echo e(isset($menu['childrenGroup']) ? 'dropdown' : ''); ?> <?php echo e(isset($menu['isFull']) && $menu['isFull'] ? 'position-static' : ''); ?>">
                                    <a class="nav-link fw-bold <?php echo e(isset($menu['childrenGroup']) ? 'dropdown-toggle' : ''); ?>"
                                        target="<?php echo e(isset($menu['new_window']) && $menu['new_window'] ? '_blank' : '_self'); ?>"
                                        href="<?php echo e($menu['link']['value'] ?: 'javascript:void(0)'); ?>">
                                        <?php echo e($menu['name'][saas_front_locale()]); ?>

                                        <?php if(isset($menu['badge']) && $menu['badge']['name'][saas_front_locale()]): ?>
                                            <span class="badge"
                                                style="background-color: <?php echo e($menu['badge']['bg_color']); ?>; color: <?php echo e($menu['badge']['text_color']); ?>; border-color: <?php echo e($menu['badge']['bg_color']); ?>">
                                                <?php echo e($menu['badge']['name'][saas_front_locale()]); ?>

                                            </span>
                                        <?php endif; ?>
                                        <?php if(isset($menu['childrenGroup'])): ?>
                                            <svg width="16" height="16" fill="none" viewBox="0 0 24 24"
                                                stroke-width="1.5" stroke="currentColor" stroke-linecap="round"
                                                stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg">
                                                <path d="m6 9 6 6 6-6" />
                                            </svg>
                                        <?php endif; ?>
                                    </a>
                                    <?php if(isset($menu['childrenGroup']) && $menu['childrenGroup']): ?>
                                        <div class="dropdown-menu <?php echo e($menu['isFull'] ? 'w-100 solution-box' : ''); ?>"
                                            style="min-width: <?php echo e(count($menu['childrenGroup']) * 180); ?>px">
                                            <div class="card card-lg">
                                                <div class="card-body">
                                                    <div class="container">
                                                        <div class="row">
                                                            <?php $__empty_1 = true; $__currentLoopData = $menu['childrenGroup']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $group): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                                                <div class="col-3 col-md">
                                                                    <?php if($group['name'][saas_front_locale()]): ?>
                                                                        <div class="fw-bold group-name">
                                                                            <?php if($group['image']): ?>
                                                                                <img src="<?php echo e(asset($group['image'])); ?>"
                                                                                    class="image_nav">
                                                                            <?php endif; ?>
                                                                            <?php echo e($group['name'][saas_front_locale()]); ?>

                                                                        </div>
                                                                    <?php endif; ?>
                                                                    <?php if($group['type'] == 'image'): ?>
                                                                        <a target="<?php echo e(isset($group['image']['link']['new_window']) && $group['image']['link']['new_window'] ? '_blank' : '_self'); ?>"
                                                                            href="<?php echo e($group['image']['link'] ?: 'javascript:void(0)'); ?>"><img
                                                                                src="<?php echo e($group['image']['image']); ?>"
                                                                                class="img-fluid"></a>
                                                                    <?php else: ?>
                                                                        <ul class="nav flex-column ul-children">
                                                                            <?php $__currentLoopData = $group['children']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $children): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                                <?php if($children['link']['text'][saas_front_locale()] ?? false): ?>
                                                                                    <li class="nav-item">
                                                                                        <?php
                                                                                            $href =
                                                                                                $children['link'][
                                                                                                    'value'
                                                                                                ] ?:
                                                                                                'javascript:void(0)';
                                                                                            if (
                                                                                                $href !==
                                                                                                'javascript:void(0)'
                                                                                            ) {
                                                                                                $host = parse_url(
                                                                                                    $href,
                                                                                                    PHP_URL_HOST,
                                                                                                );
                                                                                                if (
                                                                                                    $host &&
                                                                                                    preg_match('/^helper\./i', $host)
                                                                                                ) {
                                                                                                    $href = rtrim($href, '/');
                                                                                                    $href .= '/' . saas_front_locale();
                                                                                                }
                                                                                            }
                                                                                        ?>
                                                                                       <a
                                                                                       target="<?php echo e(isset($children['link']['new_window']) && $children['link']['new_window'] ? '_blank' : '_self'); ?>"
                                                                                       class="nav-link px-0"
                                                                                       href="<?php echo e($href); ?>"
                                                                                   >
                                                                                       <?php echo e($children['link']['text'][saas_front_locale()]); ?>

                                                                                   </a>
                                                                                    </li>
                                                                                <?php endif; ?>
                                                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                        </ul>
                                                                    <?php endif; ?>
                                                                </div>
                                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </li>
                            <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
                </div>
            </div>
            <div class="right">
                <a style="margin-left: 20px" href="<?php echo e(saas_front_route('product_pricing.index')); ?>"
                    class="navbar-link"><?php echo e(__('saas_front::home.prod_price')); ?></a>

                <?php if(current_saas_front_user()): ?>
                    <div class="dropdown user-wrap">
                        <a href="<?php echo e(saas_front_route('account.stores.index')); ?>" class="nav-link">
                            <img src="<?php echo e(asset('image/avatar.svg')); ?>" class="img-fluid avatar me-1">
                            <?php echo e(current_saas_front_user()->name); ?>

                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li class="dropdown-item">
                                <a href="<?php echo e(saas_front_route('account.edit')); ?>"
                                    class="nav-link"><?php echo e(__('shop/account.revise_info')); ?></a>
                            </li>

                            <li class="dropdown-item">
                                <a href="<?php echo e(saas_front_route('account.password')); ?>"
                                    class="nav-link"><?php echo e(__('saas_front::account.update_password')); ?></a>
                            </li>
                             <li class="dropdown-item">
                                <a href="/account/coupon/list"
                                    class="nav-link"><?php echo e(__('saas_front::account.text6')); ?></a>
                            </li>
                            <li class="dropdown-item" v-if="userData.is_invite === 1">
                                <a href="/account/promo"
                                    class="nav-link"><?php echo e(__('saas_front::account.tuiguang_dashi')); ?></a>
                            </li>
                            <li>
                                <hr class="dropdown-divider opacity-100">
                            </li>
                            <li><a href="<?php echo e(saas_front_route('logout.index')); ?>"
                                    class="dropdown-item"><?php echo e(__('saas_front::login.logout_text')); ?></a></li>
                        </ul>
                    </div>
                <?php else: ?>
                    <a href="<?php echo e(saas_front_route('login.index')); ?>"
                        class="navbar-link"><?php echo e(__('saas_front::home.text_login')); ?></a>
                    <a class="btn btn-primary btn-lg btn-register ms-3"
                        href="<?php echo e(saas_front_route('login.index')); ?>"><?php echo e(__('saas_front::home.14_free_trial')); ?></a>
                <?php endif; ?>
                <div class="dropdown saas-locales px-3">
                    <a href="<?php echo e(saas_front_route('account.stores.index')); ?>" class="nav-link">
                        <i class="bi bi-translate me-1"></i>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <?php $__currentLoopData = saas_locales(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li><a href="<?php echo e(saas_front_route('lang.switch', $item['code'])); ?>"
                                    class="dropdown-item"><?php echo e($item['name']); ?></a></li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <div class="header-mobile">
        <div class="mb-icon" data-bs-toggle="offcanvas" data-bs-target="#mobile-menu-offcanvas"
            aria-controls="offcanvasExample">
            <i class="bi bi-list"></i>
        </div>

        <div class="logo">
            <a href="<?php echo e(saas_front_route('home.index')); ?>"><img src="<?php echo e(asset('image/logo.png?v=100')); ?>"
                    class="img-fluid"></a>
        </div>
        <div class="d-flex align-items-center">
            <?php if(current_saas_front_user()): ?>
                <a class="nav-link account-menu">
                    <img src="<?php echo e(asset('image/avatar.svg')); ?>" class="img-fluid avatar me-1">
                </a>
                <button class="d-none" id="open-account-menu" data-bs-toggle="offcanvas"
                    data-bs-target="#mobile-account-menu-offcanvas" aria-controls="offcanvasAccount"></button>
            <?php else: ?>
                <a href="<?php echo e(saas_front_route('login.index')); ?>" style="white-space: nowrap;margin-left: 5px"
                    class="btn btn-outline-primary btn-sm stopPropagation"><?php echo e(__('saas_front::home.trial')); ?></a>
            <?php endif; ?>

            <div class="dropdown saas-locales ms-2 stopPropagation">
                <a class="nav-link" style="font-size: 18px">
                    <i class="bi bi-translate me-1"></i>
                </a>
                <ul class="dropdown-menu dropdown-menu-end">
                    <?php $__currentLoopData = saas_locales(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <li><a href="<?php echo e(saas_front_route('lang.switch', $item['code'])); ?>"
                                class="dropdown-item"><?php echo e($item['name']); ?></a></li>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </ul>
            </div>
        </div>

        <div class="offcanvas offcanvas-start" tabindex="-1" id="mobile-menu-offcanvas">
            <div class="offcanvas-header flex between line-cemter">
                <a class="account-icon" href="">
                    <img src="<?php echo e(asset('image/logo.png?v=100')); ?>" class="img-fluid">
                </a>
                  <a href="<?php echo e(saas_front_route('product_pricing.index')); ?>" style="white-space: nowrap;margin-left: 5px"
            class="navbar-link"><?php echo e(__('saas_front::home.prod_price')); ?></a>
            </div>
            <div class="offcanvas-body mobile-menu-wrap">
                <div class="accordion accordion-flush menu-accordion" id="menu-accordion">
                    <div class="accordion-item">
                        <div class="nav-item-text">
                            <a class="nav-link" aria-current="page"
                                href="<?php echo e(saas_front_route('home.index')); ?>"><?php echo e(__('saas_front::home.home_page')); ?></a>
                        </div>
                    </div>
                    <?php $__currentLoopData = $header_menus; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $menu): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php if($menu['name'][saas_front_locale()]): ?>
                            <div class="accordion-item">
                                <div class="nav-item-text">
                                    <a class="nav-link"
                                        target="<?php echo e(isset($menu['new_window']) && $menu['new_window'] ? '_blank' : '_self'); ?>"
                                        href="<?php echo e($menu['link']['value'] ?: '#flush-menu-' . $key); ?>"
                                        data-bs-toggle="<?php echo e(!$menu['link']['value'] ? 'collapse' : ''); ?>">
                                        <?php echo e($menu['name'][saas_front_locale()]); ?>

                                        <?php if(isset($menu['badge']) && $menu['badge']['name'][saas_front_locale()]): ?>
                                            <span class="badge"
                                                style="background-color: <?php echo e($menu['badge']['bg_color']); ?>; color: <?php echo e($menu['badge']['text_color']); ?>; border-color: <?php echo e($menu['badge']['bg_color']); ?>">
                                                <?php echo e($menu['badge']['name'][saas_front_locale()]); ?>

                                            </span>
                                        <?php endif; ?>
                                    </a>
                                    <?php if(isset($menu['childrenGroup']) && $menu['childrenGroup']): ?>
                                        <span class="collapsed" data-bs-toggle="collapse"
                                            data-bs-target="#flush-menu-<?php echo e($key); ?>"><i
                                                class="bi bi-chevron-down"></i></span>
                                    <?php endif; ?>
                                </div>
                                <?php if(isset($menu['childrenGroup']) && $menu['childrenGroup']): ?>
                                    <div class="accordion-collapse collapse" id="flush-menu-<?php echo e($key); ?>"
                                        data-bs-parent="#menu-accordion">
                                        <?php $__empty_2 = true; $__currentLoopData = $menu['childrenGroup']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $c_key => $group): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_2 = false; ?>
                                            <div class="children-group">
                                                <?php if($group['name'][saas_front_locale()]): ?>
                                                    <div class="d-flex justify-content-between align-items-center children-title"
                                                        data-bs-toggle="collapse"
                                                        data-bs-target="#children-menu-<?php echo e($key); ?>-<?php echo e($c_key); ?>">
                                                        <div><?php echo e($group['name'][saas_front_locale()]); ?></div>
                                                        <?php if($group['children']): ?>
                                                            <span class="collapsed" data-bs-toggle="collapse"
                                                                data-bs-target="#children-menu-<?php echo e($key); ?>-<?php echo e($c_key); ?>"><i
                                                                    class="bi bi-plus-lg"></i></span>
                                                        <?php endif; ?>
                                                    </div>
                                                <?php endif; ?>
                                                <div class="accordion-collapse collapse <?php echo e(!$group['name'][saas_front_locale()] ? 'd-block' : ''); ?>"
                                                    id="children-menu-<?php echo e($key); ?>-<?php echo e($c_key); ?>"
                                                    data-bs-parent="#flush-menu-<?php echo e($key); ?>">
                                                    <?php if($group['type'] == 'image'): ?>
                                                        <a target="<?php echo e(isset($group['image']['link']['new_window']) && $group['image']['link']['new_window'] ? '_blank' : '_self'); ?>"
                                                            href="<?php echo e($group['image']['link']); ?>"><img
                                                                src="<?php echo e($group['image']['image']); ?>"
                                                                class="img-fluid"></a>
                                                    <?php else: ?>
                                                        <ul class="nav flex-column ul-children">
                                                            <?php $__currentLoopData = $group['children']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $children): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                <?php if($children['link']['text'][saas_front_locale()] ?? false): ?>
                                                                    <li class="nav-item">
                                                                        <a target="<?php echo e(isset($children['link']['new_window']) && $children['link']['new_window'] ? '_blank' : '_self'); ?>"
                                                                            class="nav-link px-0"
                                                                            href="<?php echo e($children['link']['value']); ?>">
                                                                            <?php echo e($children['link']['text'][saas_front_locale()]); ?>

                                                                        </a>
                                                                    </li>
                                                                <?php endif; ?>
                                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                        </ul>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>

        <?php if(current_saas_front_user()): ?>
            <div id="mobile-account-menu-offcanvas" class="offcanvas offcanvas-start" tabindex="-1">
                <div class="offcanvas-body mobile-menu-wrap">
                    <div class="accordion accordion-flush menu-accordion">
                        <div class="accordion-item">
                            <div class="nav-item-text d-block" style="padding: 14px">
                                <img src="<?php echo e(asset('image/avatar.svg')); ?>" class="img-fluid avatar me-1">
                                <?php echo e(current_saas_front_user()->name); ?>

                            </div>
                            <div class="nav-item-text">
                                <a href="<?php echo e(saas_front_route('account.edit')); ?>"
                                    class="nav-link"><?php echo e(__('shop/account.revise_info')); ?></a>
                            </div>
                            <div class="nav-item-text">
                                <a href="<?php echo e(saas_front_route('account.password')); ?>"
                                    class="nav-link"><?php echo e(__('saas_front::account.update_password')); ?></a>
                            </div>
                            <div class="nav-item-text">
                                <a href="/account/coupon/list"
                                    class="nav-link"><?php echo e(__('saas_front::account.text6')); ?></a>
                            </div>
                            <div class="nav-item-text" v-if="userData.is_invite === 1">
                                <a href="/account/promo"
                                    class="nav-link"><?php echo e(__('saas_front::account.tuiguang_dashi')); ?></a>
                            </div>
                            <div class="nav-item-text">
                                <a href="<?php echo e(saas_front_route('logout.index')); ?>"
                                    class="dropdown-item"><?php echo e(__('saas_front::login.logout_text')); ?></a>
                            </div>

                        </div>
                    </div>
                    
                </div>
            </div>
        <?php endif; ?>
    </div>
</header>
<script src="<?php echo e(asset('vendor/vue/3x/vue.global' . (!config('app.debug') ? '.prod' : '') . '.js')); ?>"></script>

<script type="module">
    const {
        createApp,
    } = Vue3;
    const App = createApp({
        setup() {
            const userData = <?php echo json_encode(@current_saas_front_user(), 15, 512) ?>;
            return {
                userData
            }
        }
    });
    App.mount('#header-app');
    $('.account-menu').click(function() {
        let pathname = window.location.pathname
        let account = window.location.pathname.startsWith('/account')
        if ((account && pathname !== '/account/login' && pathname !== '/account/forgotten' && pathname !==
                '/account/register')) {
            $('#open-account-menu').trigger('click')
        } else {
            window.location = `<?php echo e(saas_front_route('login.index')); ?>`
        }
    });
</script>
<?php /**PATH D:\shopleadeCont\git\saas\beike\SaasFront\Providers/../Views/layout/header.blade.php ENDPATH**/ ?>