<!doctype html>
<html lang="<?php echo e(saas_front_locale()); ?>">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
  <title><?php echo $__env->yieldContent('title', system_setting('base.meta_title', 'BeikeShop开源好用的跨境电商系统')); ?></title>
  <meta name="keywords" content="<?php echo $__env->yieldContent('keywords', system_setting('base.meta_keywords')); ?>">
  <meta name="description" content="<?php echo $__env->yieldContent('description', system_setting('base.meta_description')); ?>">
  <meta name="generator" content="BeikeShop v<?php echo e(config('beike.version')); ?>(<?php echo e(config('beike.build')); ?>)">
  <base href="<?php echo e(saas_front_route('home.index')); ?>">
  <link rel="stylesheet" type="text/css" href="<?php echo e(mix('build/saasfront/css/bootstrap.css')); ?>">
  <script src="<?php echo e(asset('vendor/jquery/jquery-3.6.0.min.js')); ?>"></script>
  <script src="<?php echo e(asset('vendor/layer/3.5.1/layer.js')); ?>"></script>
  <link rel="shortcut icon" href="<?php echo e(asset('image/favicon.png')); ?>">
  <script src="<?php echo e(asset('vendor/bootstrap/js/bootstrap.bundle.min.js')); ?>"></script>
  <script src="<?php echo e(mix('build/saasfront/js/app.js')); ?>"></script>
  <link rel="stylesheet" type="text/css" href="<?php echo e(mix('build/saasfront/css/app.css')); ?>">
  <script src="<?php echo e(asset('vendor/swiper/swiper-bundle.min.js')); ?>"></script>
  <script src="<?php echo e(asset('vendor/scrolltofixed/jquery-scrolltofixed-min.js')); ?>"></script>
  <link rel="stylesheet" href="<?php echo e(asset('vendor/swiper/swiper-bundle.min.css')); ?>">
  <?php if(system_setting('base.js_content')): ?>
    <?php echo system_setting('base.js_content'); ?>

  <?php endif; ?>
  <?php echo $__env->yieldPushContent('header'); ?>
</head>
<body class="<?php echo $__env->yieldContent('body-class'); ?> <?php echo e(request('_from')); ?>">
  <?php if(!request('iframe') && request('_from') != 'app'): ?>
    <?php echo $__env->make('saas_front::layout.header', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
  <?php endif; ?>

  <div class="page-content">
    <?php echo $__env->yieldContent('content'); ?>
  </div>

  <?php if(!request('iframe') && request('_from') != 'app'): ?>
    <?php echo $__env->make('saas_front::layout.footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
  <?php endif; ?>

  <?php echo $__env->yieldPushContent('footer'); ?>
  <style>
    :root {
      --el-color-primary: #1274ff;
    }
  </style>

  <script>
    const lang = {
      file_manager: '<?php echo e(__('admin/file_manager.file_manager')); ?>',
      error_form: '<?php echo e(__('common.error_form')); ?>',
      text_hint: '<?php echo e(__('common.text_hint')); ?>',
      translate_form: '<?php echo e(__('admin/common.translate_form')); ?>',
      choose: '<?php echo e(__('common.choose')); ?>',
    }

    const config = {
      saas_files: '<?php echo e(saas_front_route('files.index')); ?>',
    }
  </script>
</body>
</html>
<?php /**PATH D:\shopleadeCont\git\saas\beike\SaasFront\Providers/../Views/layout/master.blade.php ENDPATH**/ ?>