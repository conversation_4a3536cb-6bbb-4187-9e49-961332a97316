@charset "UTF-8";

/**
 * @copyright     2022 beikeshop.com - All Rights Reserved.
 * @link          https://beikeshop.com
 * <AUTHOR> shuo <<EMAIL>>
 * @Date          2022-09-03 22:32:29
 * @LastEditTime  2022-09-16 20:55:12
 */

header {
  background-color: #fff;
  border-top: 2px solid $primary;

  .top-wrap,
  .header-content {
    .dropdown {
      &:hover {
        background-color: #fff;

        .dropdown-menu {
          margin: 0;
          display: block;
          box-shadow: 0 0 15px rgb(0, 0, 0, .1);
          border: none;

          &.dropdown-menu-end {
            right: 0;
          }
        }
      }
    }
  }

  .header-content {
    position: relative;
    padding: 20px 0 15px;

    .logo {
      img {
        max-width: 170px;
        max-height: 50px;
      }
    }

    .left-lm {
      width: 70%;
      display: flex;
      align-items: center;
      justify-content: flex-start;
    }

    .right-btn {
      width: 30%;
      display: flex;
      justify-content: flex-end;

      .nav-link {
        color: #333;
        padding-right: 0.7rem;
        padding-left: 0.7rem;
        position: relative;

        i {
          font-size: 1.3rem;
        }
      }

    }
  }

  .cart-badge-quantity {
    position: absolute;
    left: 25px;
    top: -3px;
    text-align: center;
    font-size: 12px;
    display: none;
    width: 23px;
    zoom: 0.9;
    height: 23px;
    line-height: 24px;
    background-color: $primary;
    color: #fff;
    border-radius: 50%;
  }
  .menu-box {
    &.fixed {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      z-index: 99999;
      padding: .3rem 0;
      transition: all .3s ease-in-out;
      background-color: rgba($color: #fff, $alpha: 0.9);
      box-shadow: 0 0 15px rgb(0, 0, 0, .1);
      .menu-wrap {
        border-top: none;
      }
    }
  }

  .menu-wrap {
    border-top: 1px solid #eee;

    .container {
      max-width: 1140px;
    }

    @media (min-width: 1200px) {
      .navbar-nav {
        .dropdown {

          &.position-static>.dropdown-menu {
            // top: 100%;
          }

          &:hover {
            .dropdown-menu {
              opacity: 1;
              visibility: visible;
              transform: translate(-50%);
            }
          }

          >.dropdown-menu {
            left: 50%;
            transform: translate(-50%, 0.5rem);
            transition: all .2s ease-in-out;
            transition-property: visibility, transform, opacity;
            visibility: hidden;
            opacity: 0;
            display: block;
            transform-origin: top center;
            border-color: rgba(0, 0, 0, 0.064);
            box-shadow: 0 0.5rem 2rem 0.125rem rgba(140, 152, 164, 0.286);
          }
        }
      }
    }

    >.navbar-nav {
      >.nav-item {
        background-color: transparent;
        text-transform: uppercase;
        &:hover {
          >.nav-link {
            color: $primary;
          }
        }

        >.nav-link {
          font-size: 14px;
          padding: .8rem 1.2rem;
          position: relative;

          .badge {
            position: absolute;
            bottom: 80%;
            padding: 2px 4px;
            font-weight: 400;
            border-radius: 2px;
            zoom: .8;
            left: calc(50% - 0px);
            margin-left: 0px;

            &::before {
              content: "";
              position: absolute;
              top: 100%;
              left: 10px;
              border: 4px solid;
              border-color: inherit;
              border-right-color: #0000 !important;
              border-bottom-color: #0000 !important;
              border-right-width: 7px;
              border-left-width: 0;
            }
          }
        }
      }

      .group-name {
        font-size: 15px;
      }

      .ul-children {
        a {
          color: #7a7a7a;

          &:hover {
            color: $primary;
          }
        }
      }
    }
  }

  .header-mobile {
    border-bottom: 1px solid #eee;

    &.fixed {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      z-index: 999;
      box-shadow: 0 8px 14px 0 rgba(0, 0, 0, .1);
      background: #fff;
      border-color: transparent;
    }

    .mobile-content {
      padding: 10px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      > div {
        width: 33.33%;

        &.center a {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 30px;

          img {
            max-height: 100%;
          }
        }
      }

      .left {
        display: flex;
        align-items: center;

        > div {
          cursor: pointer;
          > i {
            font-size: 1.5rem;
            line-height: 1;
          }
        }

        .mobile-open-search {
          margin-left: 12px;

          > i {
            font-size: 1.1rem;
          }
        }
      }

      .right {
        display: flex;
        justify-content: flex-end;

        .m-cart {
          .cart-badge-quantity {
            left: 11px;
            top: -9px;
            width: 20px;
            height: 20px;
            line-height: 20px;
          }
        }

        .mb-account-icon {
          span {
            margin-right: -2px;
            display: inline-block;
            vertical-align: 0.255em;
            border-top: 0.3em solid;
            border-right: 0.3em solid transparent;
            border-bottom: 0;
            border-left: 0.3em solid transparent;
          }
        }

        .nav-link {
          padding: 0;
          i {
            font-size: 1.2rem;
          }

          // margin-left: 14px;
        }
      }
    }
  }
}

#offcanvas-search-top {
  height: 100px;
  justify-content: center;

  .offcanvas-header {
    width: 100%;
  }

  input {
    &:focus {
      box-shadow: none;
    }
  }

  .btn-close {
    padding: 1rem;
    opacity: 1;

    &:hover {
      background-color: #eee;
    }
  }
}

#offcanvas-right-cart {
  z-index: 999999;

  .select-wrap {
    margin-right: 10px;
    cursor: pointer;

    i {
      font-size: 20px;
      color: #aaa;

      &.bi-check-circle-fill {
        color: $primary;
      }
    }
  }

  .offcanvas-right-products {
    .product-list {
      padding: 1rem 0;
      border-top: 1px solid #eee;

      .left {
        width: 80px;
        flex: 0 0 80px;
        height: 80px;
        border: 1px solid #eee;
        margin-right: 10px;

        img {
          max-height: 90px;
        }
      }

      .right {
        .price {
          input {
            margin-left: 10px;
            width: 50px;
            height: 24px;
          }
        }

        .offcanvas-products-delete {
          cursor: pointer;
          color: #999;
        }
      }
    }
  }

  .offcanvas-footer {}
}

#offcanvas-mobile-menu {
  width: 80%;

  .offcanvas-header {
    padding: 10px 20px 10px 10px;
  }

  .mobile-menu-wrap {
    padding: 0;

    #menu-accordion {
      border-top: 1px solid #e5e5e5;

      .accordion-item {
        border-bottom: 1px solid #e5e5e5;

        .nav-item-text {
          display: flex;
          align-items: center;
          justify-content: space-between;

          > a {
            flex: 1;
            height: 44px;
            padding-left: 10px;
            display: flex;
            align-items: center;

            .badge {
              position: relative;
              margin-left: 13px;
              // padding: 2px 4px;
              font-weight: 400;

              &::before {
                content: "";
                position: absolute;
                top: 50%;
                right: 100%;
                transform: translate(0, -50%);
                border: 4px solid;
                border-right-width: 7px;
                border: 5px solid #0000;
                border-right-color: inherit;
              }
            }
          }

          > span {
            width: 44px;
            height: 44px;
            display: flex;
            border-left: 1px solid #e5e5e5;
            align-items: center;
            justify-content: center;
            &:active {
              background-color: #eee;
            }

            &[aria-expanded="true"] {
              background-color: #eee;
              i {
                transform:rotate(180deg);
              }
            }
          }
        }

        > .accordion-collapse {
          padding: 0 10px;
          border-top: 1px solid #e5e5e5;

          .children-group {
            .children-title {
              height: 44px;
              span {
                margin-right: -10px;
                width: 44px;
                height: 42px;
                display: flex;
                align-items: center;
                justify-content: center;
                &:active {
                  background-color: #eee;
                }

                &[aria-expanded="true"] {
                  i::before {
                    content: "\F63B";
                  }
                }
              }
            }

            .nav {
              a {
                color: #777;
              }
            }
          }
        }
      }
    }
  }
}