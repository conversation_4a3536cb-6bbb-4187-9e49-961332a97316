{"__meta": {"id": "01K4P1B416FKNR5JVTN73M3GQV", "datetime": "2025-09-09 09:27:14", "utime": **********.727187, "method": "GET", "uri": "/carts/mini", "ip": "127.0.0.1"}, "php": {"version": "8.2.9", "interface": "cgi-fcgi"}, "messages": {"count": 9, "messages": [{"message": "HOOK === hook_filter: footer.content", "message_html": null, "is_string": true, "label": "log", "time": **********.592502, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === hook_filter: model.page.url", "message_html": null, "is_string": true, "label": "log", "time": **********.736104, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === hook_filter: model.page.url", "message_html": null, "is_string": true, "label": "log", "time": **********.736624, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === hook_filter: model.page.url", "message_html": null, "is_string": true, "label": "log", "time": **********.88863, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === hook_filter: model.page.url", "message_html": null, "is_string": true, "label": "log", "time": **********.888942, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === hook_filter: menu.content", "message_html": null, "is_string": true, "label": "log", "time": **********.889206, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === hook_filter: service.cart.list", "message_html": null, "is_string": true, "label": "log", "time": **********.304824, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === hook_filter: service.cart.data", "message_html": null, "is_string": true, "label": "log", "time": **********.709553, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === hook_filter: cart.mini_cart.data", "message_html": null, "is_string": true, "label": "log", "time": **********.718922, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}]}, "time": {"start": 1757381230.091023, "end": **********.727204, "duration": 4.636181116104126, "duration_str": "4.64s", "measures": [{"label": "Booting", "start": 1757381230.091023, "relative_start": 0, "end": **********.941578, "relative_end": **********.941578, "duration": 1.****************, "duration_str": "1.85s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.941587, "relative_start": 1.****************, "end": **********.727205, "relative_end": 9.5367431640625e-07, "duration": 2.****************, "duration_str": "2.79s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.947243, "relative_start": 1.***************, "end": **********.951392, "relative_end": **********.951392, "duration": 0.004148960113525391, "duration_str": "4.15ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.72449, "relative_start": 4.***************, "end": **********.724762, "relative_end": **********.724762, "duration": 0.0002720355987548828, "duration_str": "272μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.725757, "relative_start": 4.****************, "end": **********.725798, "relative_end": **********.725798, "duration": 4.100799560546875e-05, "duration_str": "41μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 1, "nb_templates": 1, "templates": [{"name": "1x cart.mini", "param_count": null, "params": [], "start": **********.712458, "type": "blade", "hash": "bladeD:\\shopleadeCont\\git\\saas\\themes\\default/cart/mini.blade.phpcart.mini", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fthemes%2Fdefault%2Fcart%2Fmini.blade.php&line=1", "ajax": false, "filename": "mini.blade.php", "line": "?"}, "render_count": 1, "name_original": "cart.mini"}]}, "route": {"uri": "GET carts/mini", "domain": "newshop.shopleade.test", "middleware": "shop", "controller": "Beike\\Shop\\Http\\Controllers\\CartController@miniCart<a href=\"phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FShop%2FHttp%2FControllers%2FCartController.php&line=158\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "shop.carts.mini", "prefix": "/", "file": "<a href=\"phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FShop%2FHttp%2FControllers%2FCartController.php&line=158\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">beike/Shop/Http/Controllers/CartController.php:158-170</a>"}, "queries": {"count": 16, "nb_statements": 16, "nb_visible_statements": 16, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 2.69194, "accumulated_duration_str": "2.69s", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select count(*) as aggregate from `saas_commissions` where `saas_store_id` = 88 and `status` = 1 and `total` >= '0.01' and `paid_at` is null", "type": "query", "params": [], "bindings": [88, 1, "0.01"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/CheckStoreExpired.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\CheckStoreExpired.php", "line": 29}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 119}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 805}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 784}], "start": **********.9553258, "duration": 0.14049, "duration_str": "140ms", "memory": 0, "memory_str": null, "filename": "CheckStoreExpired.php:29", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/CheckStoreExpired.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\CheckStoreExpired.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fapp%2FHttp%2FMiddleware%2FCheckStoreExpired.php&line=29", "ajax": false, "filename": "CheckStoreExpired.php", "line": "29"}, "connection": "wintoshop", "explain": null, "start_percent": 0, "width_percent": 5.219}, {"sql": "select * from `languages` where `status` = 1 and `languages`.`store_id` = 88 order by `sort_order` asc", "type": "query", "params": [], "bindings": [1, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "beike/Repositories/LanguageRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\LanguageRepo.php", "line": 101}, {"index": 16, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 491}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 121}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 64}], "start": 1757381232.104714, "duration": 0.14449, "duration_str": "144ms", "memory": 0, "memory_str": null, "filename": "LanguageRepo.php:101", "source": {"index": 15, "namespace": null, "name": "beike/Repositories/LanguageRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\LanguageRepo.php", "line": 101}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FRepositories%2FLanguageRepo.php&line=101", "ajax": false, "filename": "LanguageRepo.php", "line": "101"}, "connection": "wintoshop", "explain": null, "start_percent": 5.219, "width_percent": 5.368}, {"sql": "select * from `languages` where `status` = 1 and `languages`.`store_id` = 88 order by `sort_order` asc", "type": "query", "params": [], "bindings": [1, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "beike/Repositories/LanguageRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\LanguageRepo.php", "line": 101}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 45}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 25}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 19, "namespace": null, "name": "app/Http/Middleware/SetLocaleFromSession.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\SetLocaleFromSession.php", "line": 37}], "start": 1757381232.254665, "duration": 0.15117, "duration_str": "151ms", "memory": 0, "memory_str": null, "filename": "LanguageRepo.php:101", "source": {"index": 15, "namespace": null, "name": "beike/Repositories/LanguageRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\LanguageRepo.php", "line": 101}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FRepositories%2FLanguageRepo.php&line=101", "ajax": false, "filename": "LanguageRepo.php", "line": "101"}, "connection": "wintoshop", "explain": null, "start_percent": 10.586, "width_percent": 5.616}, {"sql": "select * from `pages` where `active` = 1 and `pages`.`id` = 21 and `pages`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [1, 21, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 157}, {"index": 24, "namespace": null, "name": "beike/Repositories/FooterRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\FooterRepo.php", "line": 31}, {"index": 25, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 47}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 25}], "start": 1757381232.409017, "duration": 0.15178, "duration_str": "152ms", "memory": 0, "memory_str": null, "filename": "Url.php:68", "source": {"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FLibraries%2FUrl.php&line=68", "ajax": false, "filename": "Url.php", "line": "68"}, "connection": "wintoshop", "explain": null, "start_percent": 16.202, "width_percent": 5.638}, {"sql": "select * from `pages` where `active` = 1 and `pages`.`id` = 18 and `pages`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [1, 18, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 157}, {"index": 24, "namespace": null, "name": "beike/Repositories/FooterRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\FooterRepo.php", "line": 31}, {"index": 25, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 47}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 25}], "start": 1757381232.562323, "duration": 0.14987, "duration_str": "150ms", "memory": 0, "memory_str": null, "filename": "Url.php:68", "source": {"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FLibraries%2FUrl.php&line=68", "ajax": false, "filename": "Url.php", "line": "68"}, "connection": "wintoshop", "explain": null, "start_percent": 21.84, "width_percent": 5.567}, {"sql": "select `id` from `pages` where `pages`.`store_id` = 88 order by `id` desc", "type": "query", "params": [], "bindings": [88], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "beike/Repositories/PageRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\PageRepo.php", "line": 96}, {"index": 16, "namespace": null, "name": "beike/Repositories/PageRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\PageRepo.php", "line": 80}, {"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 123}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 170}, {"index": 24, "namespace": null, "name": "beike/Repositories/FooterRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\FooterRepo.php", "line": 31}], "start": 1757381232.714162, "duration": 0.25262, "duration_str": "253ms", "memory": 0, "memory_str": null, "filename": "PageRepo.php:96", "source": {"index": 15, "namespace": null, "name": "beike/Repositories/PageRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\PageRepo.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FRepositories%2FPageRepo.php&line=96", "ajax": false, "filename": "PageRepo.php", "line": "96"}, "connection": "wintoshop", "explain": null, "start_percent": 27.408, "width_percent": 9.384}, {"sql": "select * from `page_descriptions` where `locale` = 'zh_cn' and `page_descriptions`.`page_id` in (10, 11)", "type": "query", "params": [], "bindings": ["zh_cn"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "beike/Repositories/PageRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\PageRepo.php", "line": 96}, {"index": 21, "namespace": null, "name": "beike/Repositories/PageRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\PageRepo.php", "line": 80}, {"index": 22, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 123}, {"index": 23, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 170}, {"index": 29, "namespace": null, "name": "beike/Repositories/FooterRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\FooterRepo.php", "line": 31}], "start": 1757381232.9700632, "duration": 0.14984999999999998, "duration_str": "150ms", "memory": 0, "memory_str": null, "filename": "PageRepo.php:96", "source": {"index": 20, "namespace": null, "name": "beike/Repositories/PageRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\PageRepo.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FRepositories%2FPageRepo.php&line=96", "ajax": false, "filename": "PageRepo.php", "line": "96"}, "connection": "wintoshop", "explain": null, "start_percent": 36.792, "width_percent": 5.567}, {"sql": "select * from `pages` where `active` = 1 and `pages`.`id` = 12 and `pages`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [1, 12, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 157}, {"index": 24, "namespace": null, "name": "beike/Repositories/FooterRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\FooterRepo.php", "line": 31}, {"index": 25, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 47}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 25}], "start": **********.12136, "duration": 0.15441999999999997, "duration_str": "154ms", "memory": 0, "memory_str": null, "filename": "Url.php:68", "source": {"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FLibraries%2FUrl.php&line=68", "ajax": false, "filename": "Url.php", "line": "68"}, "connection": "wintoshop", "explain": null, "start_percent": 42.359, "width_percent": 5.736}, {"sql": "select * from `pages` where `active` = 1 and `pages`.`id` = 20 and `pages`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [1, 20, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 157}, {"index": 24, "namespace": null, "name": "beike/Repositories/FooterRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\FooterRepo.php", "line": 31}, {"index": 25, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 47}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 25}], "start": **********.277972, "duration": 0.20081, "duration_str": "201ms", "memory": 0, "memory_str": null, "filename": "Url.php:68", "source": {"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FLibraries%2FUrl.php&line=68", "ajax": false, "filename": "Url.php", "line": "68"}, "connection": "wintoshop", "explain": null, "start_percent": 48.095, "width_percent": 7.46}, {"sql": "select * from `pages` where `active` = 1 and `pages`.`id` = 21 and `pages`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [1, 21, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 157}, {"index": 24, "namespace": null, "name": "beike/Repositories/FooterRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\FooterRepo.php", "line": 31}, {"index": 25, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 47}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 25}], "start": **********.480949, "duration": 0.10994, "duration_str": "110ms", "memory": 0, "memory_str": null, "filename": "Url.php:68", "source": {"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FLibraries%2FUrl.php&line=68", "ajax": false, "filename": "Url.php", "line": "68"}, "connection": "wintoshop", "explain": null, "start_percent": 55.555, "width_percent": 4.084}, {"sql": "select * from `pages` where `active` = 1 and `pages`.`id` = 11 and `pages`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [1, 11, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 157}, {"index": 21, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 48}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 25}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.5942361, "duration": 0.14055, "duration_str": "141ms", "memory": 0, "memory_str": null, "filename": "Url.php:68", "source": {"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FLibraries%2FUrl.php&line=68", "ajax": false, "filename": "Url.php", "line": "68"}, "connection": "wintoshop", "explain": null, "start_percent": 59.639, "width_percent": 5.221}, {"sql": "select * from `pages` where `active` = 1 and `pages`.`id` = 10 and `pages`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [1, 10, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 157}, {"index": 21, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 48}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 25}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.7375932, "duration": 0.15005000000000002, "duration_str": "150ms", "memory": 0, "memory_str": null, "filename": "Url.php:68", "source": {"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FLibraries%2FUrl.php&line=68", "ajax": false, "filename": "Url.php", "line": "68"}, "connection": "wintoshop", "explain": null, "start_percent": 64.86, "width_percent": 5.574}, {"sql": "select * from `customers` where `id` = 63 and `customers`.`deleted_at` is null and `customers`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [63, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 265}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.896415, "duration": 0.19548, "duration_str": "195ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "wintoshop", "explain": null, "start_percent": 70.434, "width_percent": 7.262}, {"sql": "select * from `cart_products` where `session_id` = 'YkGz0mA2y0BMpk6lkQ8Dkf8G3sQ6bHa4Ejf6HE7d' and `cart_products`.`store_id` = 88 order by `id` desc", "type": "query", "params": [], "bindings": ["YkGz0mA2y0BMpk6lkQ8Dkf8G3sQ6bHa4Ejf6HE7d", 88], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "beike/Shop/Services/CartService.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Shop\\Services\\CartService.php", "line": 46}, {"index": 16, "namespace": null, "name": "beike/Shop/Http/Controllers/CartController.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Shop\\Http\\Controllers\\CartController.php", "line": 160}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.096221, "duration": 0.20071, "duration_str": "201ms", "memory": 0, "memory_str": null, "filename": "CartService.php:46", "source": {"index": 15, "namespace": null, "name": "beike/Shop/Services/CartService.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Shop\\Services\\CartService.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FShop%2FServices%2FCartService.php&line=46", "ajax": false, "filename": "CartService.php", "line": "46"}, "connection": "wintoshop", "explain": null, "start_percent": 77.696, "width_percent": 7.456}, {"sql": "select * from `customers` where `id` = 63 and `customers`.`deleted_at` is null and `customers`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [63, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 265}, {"index": 20, "namespace": null, "name": "beike/Shop/Http/Controllers/CartController.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Shop\\Http\\Controllers\\CartController.php", "line": 161}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.3055649, "duration": 0.19648, "duration_str": "196ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "wintoshop", "explain": null, "start_percent": 85.152, "width_percent": 7.299}, {"sql": "select * from `currencies` where `status` = 1 and `currencies`.`store_id` = 88", "type": "query", "params": [], "bindings": [1, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "beike/Repositories/CurrencyRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\CurrencyRepo.php", "line": 91}, {"index": 16, "namespace": null, "name": "beike/Services/CurrencyService.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Services\\CurrencyService.php", "line": 25}, {"index": 17, "namespace": null, "name": "beike/Services/CurrencyService.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Services\\CurrencyService.php", "line": 33}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 358}, {"index": 20, "namespace": null, "name": "beike/Shop/Http/Controllers/CartController.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Shop\\Http\\Controllers\\CartController.php", "line": 161}], "start": **********.50509, "duration": 0.20323, "duration_str": "203ms", "memory": 0, "memory_str": null, "filename": "CurrencyRepo.php:91", "source": {"index": 15, "namespace": null, "name": "beike/Repositories/CurrencyRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\CurrencyRepo.php", "line": 91}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FRepositories%2FCurrencyRepo.php&line=91", "ajax": false, "filename": "CurrencyRepo.php", "line": "91"}, "connection": "wintoshop", "explain": null, "start_percent": 92.45, "width_percent": 7.55}]}, "models": {"data": {"Beike\\Models\\Language": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "Beike\\Models\\Page": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FModels%2FPage.php&line=1", "ajax": false, "filename": "Page.php", "line": "?"}}, "Beike\\Models\\Currency": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "Beike\\Models\\PageDescription": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FModels%2FPageDescription.php&line=1", "ajax": false, "filename": "PageDescription.php", "line": "?"}}}, "count": 13, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "O4jG04i8Bcl9bpHOIqVGqqnZWApkEJZpBCBit84f", "locale": "zh_cn", "login_web_shop_59ba36addc2b2f9401580f014c7f58ea4e30989d": "63", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://newshop.shopleade.test/carts/mini\"\n]", "url": "array:1 [\n  \"intended\" => \"http://jiuyia.shopleade.test/admin\"\n]", "histories": "array:6 [\n  0 => \"admin.theme.index\"\n  1 => \"admin.plugins.index\"\n  2 => \"admin.home.index\"\n  3 => \"admin.products.index\"\n  4 => \"admin.plugins.theme\"\n  5 => \"admin.settings.index\"\n]", "saas_front_locale": "zh_cn", "login_web_saas_user_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "page": "1", "login_web_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "74", "currency": "USD"}, "request": {"data": {"status": "200 OK", "full_url": "http://newshop.shopleade.test/carts/mini", "action_name": "shop.carts.mini", "controller_action": "Beike\\Shop\\Http\\Controllers\\CartController@miniCart", "uri": "GET carts/mini", "domain": "newshop.shopleade.test", "controller": "Beike\\Shop\\Http\\Controllers\\CartController@miniCart<a href=\"phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FShop%2FHttp%2FControllers%2FCartController.php&line=158\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/", "file": "<a href=\"phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FShop%2FHttp%2FControllers%2FCartController.php&line=158\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">beike/Shop/Http/Controllers/CartController.php:158-170</a>", "middleware": "shop", "duration": "4.64s", "peak_memory": "34MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-891239499 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-891239499\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-882876830 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-882876830\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-963179142 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"898 characters\">_ss_s_uid=df387fd27bfaa61c2967bdd458dc2053; beike_version={%22current%22:%*********%22%2C%22latest%22:%221.5.6%22%2C%22release_date%22:%222025-05-08%22%2C%22has_new_version%22:true}; XSRF-TOKEN=eyJpdiI6ImV6c29BL0FWQkdIYWZNQ0ZxejRDTVE9PSIsInZhbHVlIjoiVG92TGFCZlZGYllFa2xJQUJKT0tHR0hYNmtiUmc5Q250Ull4clFTS2NzM3JPRDBuUzMzeFVjckYreCtUYnQyaXpqU0o2NzJOZU4yNHRCV09zRUowRWhEMisrdGx3ZzdCQ0FBVFFsSUV5TXZFVllqUzRWaklJMlNFcitSZEVTVXAiLCJtYWMiOiIxOTExZWU1ZDExODhlMTFjNTUxYzI4ZjVjOTJiNTViMjhjOTgxMWVmMWY5M2JjZTYwZmNhMjU0ZDk0MmRkMzZjIiwidGFnIjoiIn0%3D; shopleade_session=eyJpdiI6IlhPQm5QTGVKSVFKKzY2OWQzSGNiNHc9PSIsInZhbHVlIjoicU5RMW01cmJuZG9Ic01mVU5LY1M3eTRhTEJqcldFWFFBeC9OUWQzVVV6QkRyTlFSNkgyMWxHdm5ZUVBKL21vbkszWTN0OFdNMUp2NzhRS1poYkR5UHpCa1J1TGM2ak5WT3dnOGdqMkl4dzRqYWIzb0daZWdOT2NqN25DekNGTEUiLCJtYWMiOiIxNjExOGQ1YzY1OGY5YjJkNmQ0Njc5YjUxZmNhNWZkNTQ2ZjM0Yzg3ZjVhNDhlZTNlMjQwZGUyNWNiZTNmYzVhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">zh-CN,zh;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://newshop.shopleade.test/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImV6c29BL0FWQkdIYWZNQ0ZxejRDTVE9PSIsInZhbHVlIjoiVG92TGFCZlZGYllFa2xJQUJKT0tHR0hYNmtiUmc5Q250Ull4clFTS2NzM3JPRDBuUzMzeFVjckYreCtUYnQyaXpqU0o2NzJOZU4yNHRCV09zRUowRWhEMisrdGx3ZzdCQ0FBVFFsSUV5TXZFVllqUzRWaklJMlNFcitSZEVTVXAiLCJtYWMiOiIxOTExZWU1ZDExODhlMTFjNTUxYzI4ZjVjOTJiNTViMjhjOTgxMWVmMWY5M2JjZTYwZmNhMjU0ZDk0MmRkMzZjIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">newshop.shopleade.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-963179142\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1982643206 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_ss_s_uid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>beike_version</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">O4jG04i8Bcl9bpHOIqVGqqnZWApkEJZpBCBit84f</span>\"\n  \"<span class=sf-dump-key>shopleade_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YkGz0mA2y0BMpk6lkQ8Dkf8G3sQ6bHa4Ejf6HE7d</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1982643206\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1403355413 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 09 Sep 2025 01:27:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"455 characters\">XSRF-TOKEN=eyJpdiI6InpLQzV3aFh6bXREdVI5QWNZb0t0Mnc9PSIsInZhbHVlIjoiRzlYc0x1Q3dDVkV5RHNyR3VaOHNKUmhXcW8zS2tTN2tWMWVhU05zcGN5SUJWTnpXc1FRWldycmUySW9SY3ZLdDZTMTdMMmR1R2ROanlvTUJiMUtNYUNTbTFJN0d4VjI4OVRGME5XRlFOM3VYMm5GRzRmNVFFVlRyZlNtcFN6dk4iLCJtYWMiOiI5NDU4MDUxMWY5M2FlZGM2OWMyNWIwM2MxYjEzNjFmZjlmNjQ4MjU3YmQ5MmVkMTllYTljYWRkMjM2M2Y5YzllIiwidGFnIjoiIn0%3D; expires=Thu, 09 Oct 2025 01:27:14 GMT; Max-Age=2592000; path=/; domain=.shopleade.test; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"472 characters\">shopleade_session=eyJpdiI6InRmai9LcVdwTG5hM1BOeE9YcllHb2c9PSIsInZhbHVlIjoiUmM5ZEpHY2JHZlpnY3JoS3dvZFMzYUNoQW1nelVZYk1GOW03c21tQ1FrbXVvVGJmMzR6QnRGK1VEbUptcWhBZHFiYUk0anE5K0NrSEdPQkFvM0RHajNPV1NHanl2N0cwTWkrekRUVFF6WnJQWVd4OWZveXZVUUVMRmpjNWVCdHUiLCJtYWMiOiJiZTE4OWUzYjM5ZWYwMGE0YzYyY2I3NDdlOTI5MmM2YzFlYjZkN2ZjMzMyMDFmODIwMTIwZjc4MTI1OWVmYmNhIiwidGFnIjoiIn0%3D; expires=Thu, 09 Oct 2025 01:27:14 GMT; Max-Age=2592000; path=/; domain=.shopleade.test; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"424 characters\">XSRF-TOKEN=eyJpdiI6InpLQzV3aFh6bXREdVI5QWNZb0t0Mnc9PSIsInZhbHVlIjoiRzlYc0x1Q3dDVkV5RHNyR3VaOHNKUmhXcW8zS2tTN2tWMWVhU05zcGN5SUJWTnpXc1FRWldycmUySW9SY3ZLdDZTMTdMMmR1R2ROanlvTUJiMUtNYUNTbTFJN0d4VjI4OVRGME5XRlFOM3VYMm5GRzRmNVFFVlRyZlNtcFN6dk4iLCJtYWMiOiI5NDU4MDUxMWY5M2FlZGM2OWMyNWIwM2MxYjEzNjFmZjlmNjQ4MjU3YmQ5MmVkMTllYTljYWRkMjM2M2Y5YzllIiwidGFnIjoiIn0%3D; expires=Thu, 09-Oct-2025 01:27:14 GMT; domain=.shopleade.test; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">shopleade_session=eyJpdiI6InRmai9LcVdwTG5hM1BOeE9YcllHb2c9PSIsInZhbHVlIjoiUmM5ZEpHY2JHZlpnY3JoS3dvZFMzYUNoQW1nelVZYk1GOW03c21tQ1FrbXVvVGJmMzR6QnRGK1VEbUptcWhBZHFiYUk0anE5K0NrSEdPQkFvM0RHajNPV1NHanl2N0cwTWkrekRUVFF6WnJQWVd4OWZveXZVUUVMRmpjNWVCdHUiLCJtYWMiOiJiZTE4OWUzYjM5ZWYwMGE0YzYyY2I3NDdlOTI5MmM2YzFlYjZkN2ZjMzMyMDFmODIwMTIwZjc4MTI1OWVmYmNhIiwidGFnIjoiIn0%3D; expires=Thu, 09-Oct-2025 01:27:14 GMT; domain=.shopleade.test; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1403355413\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1829661876 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">O4jG04i8Bcl9bpHOIqVGqqnZWApkEJZpBCBit84f</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"5 characters\">zh_cn</span>\"\n  \"<span class=sf-dump-key>login_web_shop_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>63</span>\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://newshop.shopleade.test/carts/mini</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://jiuyia.shopleade.test/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>histories</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">admin.theme.index</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"19 characters\">admin.plugins.index</span>\"\n    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">admin.home.index</span>\"\n    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"20 characters\">admin.products.index</span>\"\n    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"19 characters\">admin.plugins.theme</span>\"\n    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"20 characters\">admin.settings.index</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>saas_front_locale</span>\" => \"<span class=sf-dump-str title=\"5 characters\">zh_cn</span>\"\n  \"<span class=sf-dump-key>login_web_saas_user_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>login_web_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">USD</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1829661876\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://newshop.shopleade.test/carts/mini", "action_name": "shop.carts.mini", "controller_action": "Beike\\Shop\\Http\\Controllers\\CartController@miniCart"}, "badge": null}}