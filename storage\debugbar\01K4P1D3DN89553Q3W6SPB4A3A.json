{"__meta": {"id": "01K4P1D3DN89553Q3W6SPB4A3A", "datetime": "2025-09-09 09:28:19", "utime": **********.638117, "method": "GET", "uri": "/plugin/ProductSpecial/public/count-down/moment.min.js.map", "ip": "127.0.0.1"}, "php": {"version": "8.2.9", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1757381297.462603, "end": **********.638129, "duration": 2.1755259037017822, "duration_str": "2.18s", "measures": [{"label": "Booting", "start": 1757381297.462603, "relative_start": 0, "end": **********.624097, "relative_end": **********.624097, "duration": 2.***************, "duration_str": "2.16s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.624105, "relative_start": 2.***************, "end": **********.63813, "relative_end": 9.5367431640625e-07, "duration": 0.014024972915649414, "duration_str": "14.02ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.629037, "relative_start": 2.***************, "end": **********.633312, "relative_end": **********.633312, "duration": 0.004275083541870117, "duration_str": "4.28ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.634611, "relative_start": 2.****************, "end": **********.636354, "relative_end": **********.636354, "duration": 0.0017430782318115234, "duration_str": "1.74ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.636393, "relative_start": 2.****************, "end": **********.636417, "relative_end": **********.636417, "duration": 2.384185791015625e-05, "duration_str": "24μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "29MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "GET plugin/{code}/{path}", "controller": "Beike\\Shop\\Http\\Controllers\\PluginController@asset<a href=\"phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FShop%2FHttp%2FControllers%2FPluginController.php&line=19\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "shop.plugin.asset", "file": "<a href=\"phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FShop%2FHttp%2FControllers%2FPluginController.php&line=19\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">beike/Shop/Http/Controllers/PluginController.php:19-34</a>"}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"data": {"status": "200 OK", "full_url": "http://newshop.shopleade.test/plugin/ProductSpecial/public/count-down/moment.min.js.map", "action_name": "shop.plugin.asset", "controller_action": "Beike\\Shop\\Http\\Controllers\\PluginController@asset", "uri": "GET plugin/{code}/{path}", "controller": "Beike\\Shop\\Http\\Controllers\\PluginController@asset<a href=\"phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FShop%2FHttp%2FControllers%2FPluginController.php&line=19\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FShop%2FHttp%2FControllers%2FPluginController.php&line=19\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">beike/Shop/Http/Controllers/PluginController.php:19-34</a>", "duration": "2.18s", "peak_memory": "32MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1635132246 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1635132246\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1212868508 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1212868508\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-56393626 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"898 characters\">_ss_s_uid=df387fd27bfaa61c2967bdd458dc2053; beike_version={%22current%22:%*********%22%2C%22latest%22:%221.5.6%22%2C%22release_date%22:%222025-05-08%22%2C%22has_new_version%22:true}; XSRF-TOKEN=eyJpdiI6IjhiSU1XZEZhYjBoUEZLVEdtTE5yQmc9PSIsInZhbHVlIjoiUGhzVVJseGlveHhiZTJRVGpFTFhDUUlVYjVRcm9XNVpBd0dBeWJxZTFXNkdPMURHeDgvZWRrRmVTS2dXMW5XTDQ2R2RVd1lNc1BqL01ueFdKbGloUWd0VC91MGJrVld1VmhOYk85TGVKTklMKzh0RURDeXg5aWtRcDJuTFBGaHoiLCJtYWMiOiJjOWJmYjI1Y2ZiYmY1NmFhNzE3ZDQzMjQzYTY0NTkwYzViMzZlZTFkMjBiYmZjOWQ3ZmU3YzY4N2RkN2Y2MDQxIiwidGFnIjoiIn0%3D; shopleade_session=eyJpdiI6Ik5ZUXhEa1UrZlJ6ODR5QUZKeXQzOWc9PSIsInZhbHVlIjoiUE1CSWhMRUxVUGRWZ3hBcHgwMXZoVitxZFF2NkJNeHRWd3VIclk2TUNJT2VhMDh5UjFCVGRtZXBHekJXSGVzdStTbWRZNHFFdWliRzU3UjhhV08reFp1RU1vREhGbW5ZQ0RhN3c5NmczNVNmUFhQWFg0eWVXQ1FUZXJUZ2pBMloiLCJtYWMiOiI1ZDhiZjIxZGQ3NWVhZjQxNDZjYzUzNjEzNzI4OWQzZTIyNTg2OTg0ODZjY2E4NTI4YmY2ZjllOTRiZmJmNTJjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">zh-CN,zh;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">newshop.shopleade.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-56393626\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-812751943 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_ss_s_uid</span>\" => \"<span class=sf-dump-str title=\"32 characters\">df387fd27bfaa61c2967bdd458dc2053</span>\"\n  \"<span class=sf-dump-key>beike_version</span>\" => \"<span class=sf-dump-str title=\"89 characters\">{&quot;current&quot;:&quot;*******&quot;,&quot;latest&quot;:&quot;1.5.6&quot;,&quot;release_date&quot;:&quot;2025-05-08&quot;,&quot;has_new_version&quot;:true}</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjhiSU1XZEZhYjBoUEZLVEdtTE5yQmc9PSIsInZhbHVlIjoiUGhzVVJseGlveHhiZTJRVGpFTFhDUUlVYjVRcm9XNVpBd0dBeWJxZTFXNkdPMURHeDgvZWRrRmVTS2dXMW5XTDQ2R2RVd1lNc1BqL01ueFdKbGloUWd0VC91MGJrVld1VmhOYk85TGVKTklMKzh0RURDeXg5aWtRcDJuTFBGaHoiLCJtYWMiOiJjOWJmYjI1Y2ZiYmY1NmFhNzE3ZDQzMjQzYTY0NTkwYzViMzZlZTFkMjBiYmZjOWQ3ZmU3YzY4N2RkN2Y2MDQxIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>shopleade_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ik5ZUXhEa1UrZlJ6ODR5QUZKeXQzOWc9PSIsInZhbHVlIjoiUE1CSWhMRUxVUGRWZ3hBcHgwMXZoVitxZFF2NkJNeHRWd3VIclk2TUNJT2VhMDh5UjFCVGRtZXBHekJXSGVzdStTbWRZNHFFdWliRzU3UjhhV08reFp1RU1vREhGbW5ZQ0RhN3c5NmczNVNmUFhQWFg0eWVXQ1FUZXJUZ2pBMloiLCJtYWMiOiI1ZDhiZjIxZGQ3NWVhZjQxNDZjYzUzNjEzNzI4OWQzZTIyNTg2OTg0ODZjY2E4NTI4YmY2ZjllOTRiZmJmNTJjIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-812751943\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-799409514 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 09 Sep 2025 01:28:19 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-799409514\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1854066249 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1854066249\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://newshop.shopleade.test/plugin/ProductSpecial/public/count-down/moment.min.js.map", "action_name": "shop.plugin.asset", "controller_action": "Beike\\Shop\\Http\\Controllers\\PluginController@asset"}, "badge": null}}