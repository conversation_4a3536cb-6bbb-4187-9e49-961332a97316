{"__meta": {"id": "01K4P1AX7MT2KTAHX22NZKK637", "datetime": "2025-09-09 09:27:07", "utime": **********.765352, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.9", "interface": "cgi-fcgi"}, "messages": {"count": 28, "messages": [{"message": "HOOK === hook_filter: footer.content", "message_html": null, "is_string": true, "label": "log", "time": **********.083461, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === hook_filter: model.page.url", "message_html": null, "is_string": true, "label": "log", "time": **********.303996, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === hook_filter: model.page.url", "message_html": null, "is_string": true, "label": "log", "time": **********.305746, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === hook_filter: model.page.url", "message_html": null, "is_string": true, "label": "log", "time": **********.498896, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === hook_filter: model.page.url", "message_html": null, "is_string": true, "label": "log", "time": **********.499236, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === hook_filter: menu.content", "message_html": null, "is_string": true, "label": "log", "time": **********.499559, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === hook_filter: repo.product.builder", "message_html": null, "is_string": true, "label": "log", "time": **********.51046, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === hook_filter: repo.product.builder", "message_html": null, "is_string": true, "label": "log", "time": **********.711907, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === hook_filter: repo.product.builder", "message_html": null, "is_string": true, "label": "log", "time": **********.93493, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === hook_filter: home.index.data", "message_html": null, "is_string": true, "label": "log", "time": **********.517763, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hook: home.modules.before", "message_html": null, "is_string": true, "label": "log", "time": **********.523175, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hook: home.modules.after", "message_html": null, "is_string": true, "label": "log", "time": **********.535843, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hook: layout.header.code", "message_html": null, "is_string": true, "label": "log", "time": **********.538333, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hookwrapper: header.top.currency", "message_html": null, "is_string": true, "label": "log", "time": **********.137085, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hookwrapper: header.top.language", "message_html": null, "is_string": true, "label": "log", "time": **********.335487, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hookwrapper: header.top.telephone", "message_html": null, "is_string": true, "label": "log", "time": **********.335525, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hookwrapper: header.menu.logo", "message_html": null, "is_string": true, "label": "log", "time": **********.335579, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hookwrapper: header.menu.icon", "message_html": null, "is_string": true, "label": "log", "time": **********.541386, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hook: header.menu.before", "message_html": null, "is_string": true, "label": "log", "time": **********.553929, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hook: header.menu.after", "message_html": null, "is_string": true, "label": "log", "time": **********.554037, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hook: footer.before", "message_html": null, "is_string": true, "label": "log", "time": **********.558973, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hook: footer.services.before", "message_html": null, "is_string": true, "label": "log", "time": **********.559005, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hook: footer.services.after", "message_html": null, "is_string": true, "label": "log", "time": **********.559872, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hook: footer.contact.before", "message_html": null, "is_string": true, "label": "log", "time": **********.560277, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hookwrapper: footer.contact", "message_html": null, "is_string": true, "label": "log", "time": **********.560341, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hook: footer.contact.after", "message_html": null, "is_string": true, "label": "log", "time": **********.560357, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hookwrapper: footer.copyright", "message_html": null, "is_string": true, "label": "log", "time": **********.560396, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}, {"message": "HOOK === @hook: footer.after", "message_html": null, "is_string": true, "label": "log", "time": **********.56041, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Fbarryvdh%2Flaravel-debugbar%2Fsrc%2FLaravelDebugbar.php&line=1200", "ajax": false, "filename": "LaravelDebugbar.php", "line": "1200"}}]}, "time": {"start": 1757381220.968039, "end": **********.765382, "duration": 6.797343015670776, "duration_str": "6.8s", "measures": [{"label": "Booting", "start": 1757381220.968039, "relative_start": 0, "end": **********.01236, "relative_end": **********.01236, "duration": 2.***************, "duration_str": "2.04s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.012372, "relative_start": 2.***************, "end": **********.765384, "relative_end": 1.9073486328125e-06, "duration": 4.**************, "duration_str": "4.75s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.022628, "relative_start": 2.***************, "end": **********.039024, "relative_end": **********.039024, "duration": 0.016396045684814453, "duration_str": "16.4ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.519163, "relative_start": 5.***************, "end": **********.759579, "relative_end": **********.759579, "duration": 1.****************, "duration_str": "1.24s", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.762834, "relative_start": 6.***************, "end": **********.762911, "relative_end": **********.762911, "duration": 7.700920104980469e-05, "duration_str": "77μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 18, "nb_templates": 18, "templates": [{"name": "1x home", "param_count": null, "params": [], "start": **********.520777, "type": "blade", "hash": "bladeD:\\shopleadeCont\\git\\saas\\themes\\default/home.blade.phphome", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fthemes%2Fdefault%2Fhome.blade.php&line=1", "ajax": false, "filename": "home.blade.php", "line": "?"}, "render_count": 1, "name_original": "home"}, {"name": "1x GoogleAnalytics::view_content", "param_count": null, "params": [], "start": **********.524568, "type": "blade", "hash": "bladeD:\\shopleadeCont\\git\\saas\\plugins/GoogleAnalytics/Views/view_content.blade.phpGoogleAnalytics::view_content", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fplugins%2FGoogleAnalytics%2FViews%2Fview_content.blade.php&line=1", "ajax": false, "filename": "view_content.blade.php", "line": "?"}, "render_count": 1, "name_original": "GoogleAnalytics::view_content"}, {"name": "1x MetaPixel::view_content", "param_count": null, "params": [], "start": **********.526246, "type": "blade", "hash": "bladeD:\\shopleadeCont\\git\\saas\\plugins/MetaPixel/Views/view_content.blade.phpMetaPixel::view_content", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fplugins%2FMetaPixel%2FViews%2Fview_content.blade.php&line=1", "ajax": false, "filename": "view_content.blade.php", "line": "?"}, "render_count": 1, "name_original": "MetaPixel::view_content"}, {"name": "1x TwitterPixel::view_content", "param_count": null, "params": [], "start": **********.527451, "type": "blade", "hash": "bladeD:\\shopleadeCont\\git\\saas\\plugins/TwitterPixel/Views/view_content.blade.phpTwitterPixel::view_content", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fplugins%2FTwitterPixel%2FViews%2Fview_content.blade.php&line=1", "ajax": false, "filename": "view_content.blade.php", "line": "?"}, "render_count": 1, "name_original": "TwitterPixel::view_content"}, {"name": "1x design.tab_product", "param_count": null, "params": [], "start": **********.528102, "type": "blade", "hash": "bladeD:\\shopleadeCont\\git\\saas\\themes\\default/design/tab_product.blade.phpdesign.tab_product", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fthemes%2Fdefault%2Fdesign%2Ftab_product.blade.php&line=1", "ajax": false, "filename": "tab_product.blade.php", "line": "?"}, "render_count": 1, "name_original": "design.tab_product"}, {"name": "3x design._partial._module_tool", "param_count": null, "params": [], "start": **********.530683, "type": "blade", "hash": "bladeD:\\shopleadeCont\\git\\saas\\themes\\default/design/_partial/_module_tool.blade.phpdesign._partial._module_tool", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fthemes%2Fdefault%2Fdesign%2F_partial%2F_module_tool.blade.php&line=1", "ajax": false, "filename": "_module_tool.blade.php", "line": "?"}, "render_count": 3, "name_original": "design._partial._module_tool"}, {"name": "1x design.brand", "param_count": null, "params": [], "start": **********.532246, "type": "blade", "hash": "bladeD:\\shopleadeCont\\git\\saas\\themes\\default/design/brand.blade.phpdesign.brand", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fthemes%2Fdefault%2Fdesign%2Fbrand.blade.php&line=1", "ajax": false, "filename": "brand.blade.php", "line": "?"}, "render_count": 1, "name_original": "design.brand"}, {"name": "1x design.page", "param_count": null, "params": [], "start": **********.534124, "type": "blade", "hash": "bladeD:\\shopleadeCont\\git\\saas\\themes\\default/design/page.blade.phpdesign.page", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fthemes%2Fdefault%2Fdesign%2Fpage.blade.php&line=1", "ajax": false, "filename": "page.blade.php", "line": "?"}, "render_count": 1, "name_original": "design.page"}, {"name": "1x layout.master", "param_count": null, "params": [], "start": **********.536298, "type": "blade", "hash": "bladeD:\\shopleadeCont\\git\\saas\\themes\\fashion/layout/master.blade.phplayout.master", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fthemes%2Ffashion%2Flayout%2Fmaster.blade.php&line=1", "ajax": false, "filename": "master.blade.php", "line": "?"}, "render_count": 1, "name_original": "layout.master"}, {"name": "1x GoogleAnalytics::layout_header_code", "param_count": null, "params": [], "start": **********.538495, "type": "blade", "hash": "bladeD:\\shopleadeCont\\git\\saas\\plugins/GoogleAnalytics/Views/layout_header_code.blade.phpGoogleAnalytics::layout_header_code", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fplugins%2FGoogleAnalytics%2FViews%2Flayout_header_code.blade.php&line=1", "ajax": false, "filename": "layout_header_code.blade.php", "line": "?"}, "render_count": 1, "name_original": "GoogleAnalytics::layout_header_code"}, {"name": "1x TikTokPixel::layout_header_code", "param_count": null, "params": [], "start": **********.927894, "type": "blade", "hash": "bladeD:\\shopleadeCont\\git\\saas\\plugins/TikTokPixel/Views/layout_header_code.blade.phpTikTokPixel::layout_header_code", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fplugins%2FTikTokPixel%2FViews%2Flayout_header_code.blade.php&line=1", "ajax": false, "filename": "layout_header_code.blade.php", "line": "?"}, "render_count": 1, "name_original": "TikTokPixel::layout_header_code"}, {"name": "1x SaleSmartly::layout_header_code", "param_count": null, "params": [], "start": **********.928635, "type": "blade", "hash": "bladeD:\\shopleadeCont\\git\\saas\\plugins/SaleSmartly/Views/layout_header_code.blade.phpSaleSmartly::layout_header_code", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fplugins%2FSaleSmartly%2FViews%2Flayout_header_code.blade.php&line=1", "ajax": false, "filename": "layout_header_code.blade.php", "line": "?"}, "render_count": 1, "name_original": "SaleSmartly::layout_header_code"}, {"name": "1x TwitterPixel::layout_header_code", "param_count": null, "params": [], "start": **********.928959, "type": "blade", "hash": "bladeD:\\shopleadeCont\\git\\saas\\plugins/TwitterPixel/Views/layout_header_code.blade.phpTwitterPixel::layout_header_code", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fplugins%2FTwitterPixel%2FViews%2Flayout_header_code.blade.php&line=1", "ajax": false, "filename": "layout_header_code.blade.php", "line": "?"}, "render_count": 1, "name_original": "TwitterPixel::layout_header_code"}, {"name": "1x layout.header", "param_count": null, "params": [], "start": **********.92947, "type": "blade", "hash": "bladeD:\\shopleadeCont\\git\\saas\\themes\\fashion/layout/header.blade.phplayout.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fthemes%2Ffashion%2Flayout%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "layout.header"}, {"name": "1x shared.menu-pc", "param_count": null, "params": [], "start": **********.551848, "type": "blade", "hash": "bladeD:\\shopleadeCont\\git\\saas\\themes\\fashion/shared/menu-pc.blade.phpshared.menu-pc", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fthemes%2Ffashion%2Fshared%2Fmenu-pc.blade.php&line=1", "ajax": false, "filename": "menu-pc.blade.php", "line": "?"}, "render_count": 1, "name_original": "shared.menu-pc"}, {"name": "1x layout.footer", "param_count": null, "params": [], "start": **********.554594, "type": "blade", "hash": "bladeD:\\shopleadeCont\\git\\saas\\themes\\fashion/layout/footer.blade.phplayout.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fthemes%2Ffashion%2Flayout%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "layout.footer"}]}, "route": {"uri": "GET /", "domain": "newshop.shopleade.test", "middleware": "shop", "controller": "Beike\\Shop\\Http\\Controllers\\HomeController@index<a href=\"phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FShop%2FHttp%2FControllers%2FHomeController.php&line=22\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "shop.home.index", "prefix": "/", "file": "<a href=\"phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FShop%2FHttp%2FControllers%2FHomeController.php&line=22\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">beike/Shop/Http/Controllers/HomeController.php:22-100</a>"}, "queries": {"count": 23, "nb_statements": 23, "nb_visible_statements": 23, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 4.548869999999999, "accumulated_duration_str": "4.55s", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select count(*) as aggregate from `saas_commissions` where `saas_store_id` = 88 and `status` = 1 and `total` >= '0.01' and `paid_at` is null", "type": "query", "params": [], "bindings": [88, 1, "0.01"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/CheckStoreExpired.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\CheckStoreExpired.php", "line": 29}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 119}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 805}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 784}], "start": **********.0458128, "duration": 0.19157, "duration_str": "192ms", "memory": 0, "memory_str": null, "filename": "CheckStoreExpired.php:29", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/CheckStoreExpired.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\CheckStoreExpired.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fapp%2FHttp%2FMiddleware%2FCheckStoreExpired.php&line=29", "ajax": false, "filename": "CheckStoreExpired.php", "line": "29"}, "connection": "wintoshop", "explain": null, "start_percent": 0, "width_percent": 4.211}, {"sql": "select * from `languages` where `status` = 1 and `languages`.`store_id` = 88 order by `sort_order` asc", "type": "query", "params": [], "bindings": [1, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "beike/Repositories/LanguageRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\LanguageRepo.php", "line": 101}, {"index": 16, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 491}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 121}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 64}], "start": **********.261949, "duration": 0.18678, "duration_str": "187ms", "memory": 0, "memory_str": null, "filename": "LanguageRepo.php:101", "source": {"index": 15, "namespace": null, "name": "beike/Repositories/LanguageRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\LanguageRepo.php", "line": 101}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FRepositories%2FLanguageRepo.php&line=101", "ajax": false, "filename": "LanguageRepo.php", "line": "101"}, "connection": "wintoshop", "explain": null, "start_percent": 4.211, "width_percent": 4.106}, {"sql": "select * from `languages` where `status` = 1 and `languages`.`store_id` = 88 order by `sort_order` asc", "type": "query", "params": [], "bindings": [1, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "beike/Repositories/LanguageRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\LanguageRepo.php", "line": 101}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 45}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 25}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 19, "namespace": null, "name": "app/Http/Middleware/SetLocaleFromSession.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\SetLocaleFromSession.php", "line": 37}], "start": **********.456863, "duration": 0.19022, "duration_str": "190ms", "memory": 0, "memory_str": null, "filename": "LanguageRepo.php:101", "source": {"index": 15, "namespace": null, "name": "beike/Repositories/LanguageRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\LanguageRepo.php", "line": 101}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FRepositories%2FLanguageRepo.php&line=101", "ajax": false, "filename": "LanguageRepo.php", "line": "101"}, "connection": "wintoshop", "explain": null, "start_percent": 8.317, "width_percent": 4.182}, {"sql": "select * from `pages` where `active` = 1 and `pages`.`id` = 21 and `pages`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [1, 21, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 157}, {"index": 24, "namespace": null, "name": "beike/Repositories/FooterRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\FooterRepo.php", "line": 31}, {"index": 25, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 47}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 25}], "start": **********.651039, "duration": 0.20285, "duration_str": "203ms", "memory": 0, "memory_str": null, "filename": "Url.php:68", "source": {"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FLibraries%2FUrl.php&line=68", "ajax": false, "filename": "Url.php", "line": "68"}, "connection": "wintoshop", "explain": null, "start_percent": 12.499, "width_percent": 4.459}, {"sql": "select * from `pages` where `active` = 1 and `pages`.`id` = 18 and `pages`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [1, 18, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 157}, {"index": 24, "namespace": null, "name": "beike/Repositories/FooterRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\FooterRepo.php", "line": 31}, {"index": 25, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 47}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 25}], "start": **********.858608, "duration": 0.19973, "duration_str": "200ms", "memory": 0, "memory_str": null, "filename": "Url.php:68", "source": {"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FLibraries%2FUrl.php&line=68", "ajax": false, "filename": "Url.php", "line": "68"}, "connection": "wintoshop", "explain": null, "start_percent": 16.958, "width_percent": 4.391}, {"sql": "select `id` from `pages` where `pages`.`store_id` = 88 order by `id` desc", "type": "query", "params": [], "bindings": [88], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "beike/Repositories/PageRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\PageRepo.php", "line": 96}, {"index": 16, "namespace": null, "name": "beike/Repositories/PageRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\PageRepo.php", "line": 80}, {"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 123}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 170}, {"index": 24, "namespace": null, "name": "beike/Repositories/FooterRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\FooterRepo.php", "line": 31}], "start": 1757381224.061651, "duration": 0.19984, "duration_str": "200ms", "memory": 0, "memory_str": null, "filename": "PageRepo.php:96", "source": {"index": 15, "namespace": null, "name": "beike/Repositories/PageRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\PageRepo.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FRepositories%2FPageRepo.php&line=96", "ajax": false, "filename": "PageRepo.php", "line": "96"}, "connection": "wintoshop", "explain": null, "start_percent": 21.349, "width_percent": 4.393}, {"sql": "select * from `page_descriptions` where `locale` = 'zh_cn' and `page_descriptions`.`page_id` in (10, 11)", "type": "query", "params": [], "bindings": ["zh_cn"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "beike/Repositories/PageRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\PageRepo.php", "line": 96}, {"index": 21, "namespace": null, "name": "beike/Repositories/PageRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\PageRepo.php", "line": 80}, {"index": 22, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 123}, {"index": 23, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 170}, {"index": 29, "namespace": null, "name": "beike/Repositories/FooterRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\FooterRepo.php", "line": 31}], "start": 1757381224.267536, "duration": 0.15228999999999998, "duration_str": "152ms", "memory": 0, "memory_str": null, "filename": "PageRepo.php:96", "source": {"index": 20, "namespace": null, "name": "beike/Repositories/PageRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\PageRepo.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FRepositories%2FPageRepo.php&line=96", "ajax": false, "filename": "PageRepo.php", "line": "96"}, "connection": "wintoshop", "explain": null, "start_percent": 25.742, "width_percent": 3.348}, {"sql": "select * from `pages` where `active` = 1 and `pages`.`id` = 12 and `pages`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [1, 12, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 157}, {"index": 24, "namespace": null, "name": "beike/Repositories/FooterRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\FooterRepo.php", "line": 31}, {"index": 25, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 47}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 25}], "start": 1757381224.420679, "duration": 0.25203, "duration_str": "252ms", "memory": 0, "memory_str": null, "filename": "Url.php:68", "source": {"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FLibraries%2FUrl.php&line=68", "ajax": false, "filename": "Url.php", "line": "68"}, "connection": "wintoshop", "explain": null, "start_percent": 29.09, "width_percent": 5.54}, {"sql": "select * from `pages` where `active` = 1 and `pages`.`id` = 20 and `pages`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [1, 20, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 157}, {"index": 24, "namespace": null, "name": "beike/Repositories/FooterRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\FooterRepo.php", "line": 31}, {"index": 25, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 47}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 25}], "start": 1757381224.67733, "duration": 0.19946, "duration_str": "199ms", "memory": 0, "memory_str": null, "filename": "Url.php:68", "source": {"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FLibraries%2FUrl.php&line=68", "ajax": false, "filename": "Url.php", "line": "68"}, "connection": "wintoshop", "explain": null, "start_percent": 34.631, "width_percent": 4.385}, {"sql": "select * from `pages` where `active` = 1 and `pages`.`id` = 21 and `pages`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [1, 21, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 157}, {"index": 24, "namespace": null, "name": "beike/Repositories/FooterRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\FooterRepo.php", "line": 31}, {"index": 25, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 47}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 25}], "start": 1757381224.877852, "duration": 0.20283, "duration_str": "203ms", "memory": 0, "memory_str": null, "filename": "Url.php:68", "source": {"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FLibraries%2FUrl.php&line=68", "ajax": false, "filename": "Url.php", "line": "68"}, "connection": "wintoshop", "explain": null, "start_percent": 39.016, "width_percent": 4.459}, {"sql": "select * from `pages` where `active` = 1 and `pages`.`id` = 11 and `pages`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [1, 11, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 157}, {"index": 21, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 48}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 25}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.084742, "duration": 0.21875999999999998, "duration_str": "219ms", "memory": 0, "memory_str": null, "filename": "Url.php:68", "source": {"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FLibraries%2FUrl.php&line=68", "ajax": false, "filename": "Url.php", "line": "68"}, "connection": "wintoshop", "explain": null, "start_percent": 43.475, "width_percent": 4.809}, {"sql": "select * from `pages` where `active` = 1 and `pages`.`id` = 10 and `pages`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [1, 10, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 157}, {"index": 21, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 48}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/ShareViewData.php", "file": "D:\\shopleadeCont\\git\\saas\\app\\Http\\Middleware\\ShareViewData.php", "line": 25}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.306334, "duration": 0.19163, "duration_str": "192ms", "memory": 0, "memory_str": null, "filename": "Url.php:68", "source": {"index": 17, "namespace": null, "name": "beike/Libraries/Url.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Libraries\\Url.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FLibraries%2FUrl.php&line=68", "ajax": false, "filename": "Url.php", "line": "68"}, "connection": "wintoshop", "explain": null, "start_percent": 48.284, "width_percent": 4.213}, {"sql": "select `products`.*, `pd`.`name`, `pd`.`content`, `pd`.`meta_title`, `pd`.`meta_description`, `pd`.`meta_keywords`, `pd`.`name` from `products` left join `product_descriptions` as `pd` on `pd`.`product_id` = `products`.`id` and `locale` = 'zh_cn' where `products`.`id` in (5, 9, 10, 11, 12, 13, 14, 15) and exists (select * from `product_skus` where `products`.`id` = `product_skus`.`product_id` and `is_default` = 1 and `product_skus`.`store_id` = 88) and `products`.`deleted_at` is null and `products`.`store_id` = 88 order by FIELD(products.id, 5,9,10,11,12,13,14,15), `products`.`position` asc", "type": "query", "params": [], "bindings": ["zh_cn", 5, 9, 10, 11, 12, 13, 14, 15, 1, 88, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "beike/Repositories/ProductRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\ProductRepo.php", "line": 77}, {"index": 16, "namespace": null, "name": "beike/Services/DesignService.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Services\\DesignService.php", "line": 211}, {"index": 17, "namespace": null, "name": "beike/Services/DesignService.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Services\\DesignService.php", "line": 61}, {"index": 18, "namespace": null, "name": "beike/Shop/Http/Controllers/HomeController.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Shop\\Http\\Controllers\\HomeController.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.5114782, "duration": 0.*****************, "duration_str": "191ms", "memory": 0, "memory_str": null, "filename": "ProductRepo.php:77", "source": {"index": 15, "namespace": null, "name": "beike/Repositories/ProductRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\ProductRepo.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FRepositories%2FProductRepo.php&line=77", "ajax": false, "filename": "ProductRepo.php", "line": "77"}, "connection": "wintoshop", "explain": null, "start_percent": 52.496, "width_percent": 4.209}, {"sql": "select `products`.*, `pd`.`name`, `pd`.`content`, `pd`.`meta_title`, `pd`.`meta_description`, `pd`.`meta_keywords`, `pd`.`name` from `products` left join `product_descriptions` as `pd` on `pd`.`product_id` = `products`.`id` and `locale` = 'zh_cn' where `products`.`id` in (39, 15, 1, 4, 13, 7, 8, 4) and exists (select * from `product_skus` where `products`.`id` = `product_skus`.`product_id` and `is_default` = 1 and `product_skus`.`store_id` = 88) and `products`.`deleted_at` is null and `products`.`store_id` = 88 order by FIELD(products.id, 39,15,1,4,13,7,8,4), `products`.`position` asc", "type": "query", "params": [], "bindings": ["zh_cn", 39, 15, 1, 4, 13, 7, 8, 4, 1, 88, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "beike/Repositories/ProductRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\ProductRepo.php", "line": 77}, {"index": 16, "namespace": null, "name": "beike/Services/DesignService.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Services\\DesignService.php", "line": 211}, {"index": 17, "namespace": null, "name": "beike/Services/DesignService.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Services\\DesignService.php", "line": 61}, {"index": 18, "namespace": null, "name": "beike/Shop/Http/Controllers/HomeController.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Shop\\Http\\Controllers\\HomeController.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.712395, "duration": 0.22194, "duration_str": "222ms", "memory": 0, "memory_str": null, "filename": "ProductRepo.php:77", "source": {"index": 15, "namespace": null, "name": "beike/Repositories/ProductRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\ProductRepo.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FRepositories%2FProductRepo.php&line=77", "ajax": false, "filename": "ProductRepo.php", "line": "77"}, "connection": "wintoshop", "explain": null, "start_percent": 56.705, "width_percent": 4.879}, {"sql": "select `products`.*, `pd`.`name`, `pd`.`content`, `pd`.`meta_title`, `pd`.`meta_description`, `pd`.`meta_keywords`, `pd`.`name` from `products` left join `product_descriptions` as `pd` on `pd`.`product_id` = `products`.`id` and `locale` = 'zh_cn' where `products`.`id` in (1, 2, 3, 4, 5, 7, 8, 11) and exists (select * from `product_skus` where `products`.`id` = `product_skus`.`product_id` and `is_default` = 1 and `product_skus`.`store_id` = 88) and `products`.`deleted_at` is null and `products`.`store_id` = 88 order by FIELD(products.id, 1,2,3,4,5,7,8,11), `products`.`position` asc", "type": "query", "params": [], "bindings": ["zh_cn", 1, 2, 3, 4, 5, 7, 8, 11, 1, 88, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "beike/Repositories/ProductRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\ProductRepo.php", "line": 77}, {"index": 16, "namespace": null, "name": "beike/Services/DesignService.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Services\\DesignService.php", "line": 211}, {"index": 17, "namespace": null, "name": "beike/Services/DesignService.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Services\\DesignService.php", "line": 61}, {"index": 18, "namespace": null, "name": "beike/Shop/Http/Controllers/HomeController.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Shop\\Http\\Controllers\\HomeController.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.935421, "duration": 0.18584, "duration_str": "186ms", "memory": 0, "memory_str": null, "filename": "ProductRepo.php:77", "source": {"index": 15, "namespace": null, "name": "beike/Repositories/ProductRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\ProductRepo.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FRepositories%2FProductRepo.php&line=77", "ajax": false, "filename": "ProductRepo.php", "line": "77"}, "connection": "wintoshop", "explain": null, "start_percent": 61.584, "width_percent": 4.085}, {"sql": "select * from `brands` where `id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12) and `brands`.`store_id` = 88 order by FIELD(id, 1,2,3,4,5,6,7,8,9,10,11,12)", "type": "query", "params": [], "bindings": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "beike/Repositories/BrandRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\BrandRepo.php", "line": 183}, {"index": 16, "namespace": null, "name": "beike/Services/DesignService.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Services\\DesignService.php", "line": 122}, {"index": 17, "namespace": null, "name": "beike/Services/DesignService.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Services\\DesignService.php", "line": 59}, {"index": 18, "namespace": null, "name": "beike/Shop/Http/Controllers/HomeController.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Shop\\Http\\Controllers\\HomeController.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.127496, "duration": 0.*****************, "duration_str": "184ms", "memory": 0, "memory_str": null, "filename": "BrandRepo.php:183", "source": {"index": 15, "namespace": null, "name": "beike/Repositories/BrandRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\BrandRepo.php", "line": 183}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FRepositories%2FBrandRepo.php&line=183", "ajax": false, "filename": "BrandRepo.php", "line": "183"}, "connection": "wintoshop", "explain": null, "start_percent": 65.67, "width_percent": 4.055}, {"sql": "select * from `pages` where `id` in (22, 23, 24, 25) and `active` = 1 and `pages`.`store_id` = 88", "type": "query", "params": [], "bindings": [22, 23, 24, 25, 1, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "beike/Admin/Repositories/PageRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Admin\\Repositories\\PageRepo.php", "line": 142}, {"index": 16, "namespace": null, "name": "beike/Services/DesignService.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Services\\DesignService.php", "line": 229}, {"index": 17, "namespace": null, "name": "beike/Services/DesignService.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Services\\DesignService.php", "line": 69}, {"index": 18, "namespace": null, "name": "beike/Shop/Http/Controllers/HomeController.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Shop\\Http\\Controllers\\HomeController.php", "line": 57}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.315377, "duration": 0.19913, "duration_str": "199ms", "memory": 0, "memory_str": null, "filename": "PageRepo.php:142", "source": {"index": 15, "namespace": null, "name": "beike/Admin/Repositories/PageRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Admin\\Repositories\\PageRepo.php", "line": 142}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FAdmin%2FRepositories%2FPageRepo.php&line=142", "ajax": false, "filename": "PageRepo.php", "line": "142"}, "connection": "wintoshop", "explain": null, "start_percent": 69.725, "width_percent": 4.378}, {"sql": "select * from `customers` where `id` = 63 and `customers`.`deleted_at` is null and `customers`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [63, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 265}, {"index": 22, "namespace": null, "name": "beike/Hook/Hook.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Hook\\Hook.php", "line": 40}, {"index": 23, "namespace": null, "name": "beike/Hook/Hook.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Hook\\Hook.php", "line": 64}], "start": **********.542186, "duration": 0.17861000000000002, "duration_str": "179ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "wintoshop", "explain": null, "start_percent": 74.102, "width_percent": 3.926}, {"sql": "select * from `customers` where `id` = 63 and `customers`.`deleted_at` is null and `customers`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [63, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 265}, {"index": 22, "namespace": null, "name": "beike/Hook/Hook.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Hook\\Hook.php", "line": 40}, {"index": 23, "namespace": null, "name": "beike/Hook/Hook.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Hook\\Hook.php", "line": 64}], "start": **********.723161, "duration": 0.2026, "duration_str": "203ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "wintoshop", "explain": null, "start_percent": 78.029, "width_percent": 4.454}, {"sql": "select * from `currencies` where `status` = 1 and `currencies`.`store_id` = 88", "type": "query", "params": [], "bindings": [1, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "beike/Repositories/CurrencyRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\CurrencyRepo.php", "line": 91}, {"index": 16, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 537}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.9337351, "duration": 0.20253, "duration_str": "203ms", "memory": 0, "memory_str": null, "filename": "CurrencyRepo.php:91", "source": {"index": 15, "namespace": null, "name": "beike/Repositories/CurrencyRepo.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Repositories\\CurrencyRepo.php", "line": 91}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FRepositories%2FCurrencyRepo.php&line=91", "ajax": false, "filename": "CurrencyRepo.php", "line": "91"}, "connection": "wintoshop", "explain": null, "start_percent": 82.482, "width_percent": 4.452}, {"sql": "select * from `languages` where `code` = 'zh_cn' and `languages`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": ["zh_cn", 88], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 503}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.137335, "duration": 0.19763, "duration_str": "198ms", "memory": 0, "memory_str": null, "filename": "Helpers.php:503", "source": {"index": 16, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 503}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FHelpers.php&line=503", "ajax": false, "filename": "Helpers.php", "line": "503"}, "connection": "wintoshop", "explain": null, "start_percent": 86.935, "width_percent": 4.345}, {"sql": "select * from `customers` where `id` = 63 and `customers`.`deleted_at` is null and `customers`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [63, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "view", "name": "layout.header", "file": "D:\\shopleadeCont\\git\\saas\\themes\\fashion/layout/header.blade.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.338261, "duration": 0.20004, "duration_str": "200ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "wintoshop", "explain": null, "start_percent": 91.279, "width_percent": 4.398}, {"sql": "select * from `customers` where `id` = 63 and `customers`.`deleted_at` is null and `customers`.`store_id` = 88 limit 1", "type": "query", "params": [], "bindings": [63, 88], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "beike/Helpers.php", "file": "D:\\shopleadeCont\\git\\saas\\beike\\Helpers.php", "line": 265}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.560705, "duration": 0.19665000000000002, "duration_str": "197ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\shopleadeCont\\git\\saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "wintoshop", "explain": null, "start_percent": 95.677, "width_percent": 4.323}]}, "models": {"data": {"Beike\\Models\\Language": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "Beike\\Models\\Page": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FModels%2FPage.php&line=1", "ajax": false, "filename": "Page.php", "line": "?"}}, "Beike\\Models\\Currency": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "Beike\\Models\\PageDescription": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FModels%2FPageDescription.php&line=1", "ajax": false, "filename": "PageDescription.php", "line": "?"}}}, "count": 14, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "O4jG04i8Bcl9bpHOIqVGqqnZWApkEJZpBCBit84f", "locale": "zh_cn", "login_web_shop_59ba36addc2b2f9401580f014c7f58ea4e30989d": "63", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://newshop.shopleade.test\"\n]", "url": "array:1 [\n  \"intended\" => \"http://jiuyia.shopleade.test/admin\"\n]", "histories": "array:6 [\n  0 => \"admin.theme.index\"\n  1 => \"admin.plugins.index\"\n  2 => \"admin.home.index\"\n  3 => \"admin.products.index\"\n  4 => \"admin.plugins.theme\"\n  5 => \"admin.settings.index\"\n]", "saas_front_locale": "zh_cn", "login_web_saas_user_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "page": "1", "login_web_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "74", "currency": "USD"}, "request": {"data": {"status": "200 OK", "full_url": "http://newshop.shopleade.test", "action_name": "shop.home.index", "controller_action": "Beike\\Shop\\Http\\Controllers\\HomeController@index", "uri": "GET /", "domain": "newshop.shopleade.test", "controller": "Beike\\Shop\\Http\\Controllers\\HomeController@index<a href=\"phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FShop%2FHttp%2FControllers%2FHomeController.php&line=22\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/", "file": "<a href=\"phpstorm://open?file=D%3A%2FshopleadeCont%2Fgit%2Fsaas%2Fbeike%2FShop%2FHttp%2FControllers%2FHomeController.php&line=22\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">beike/Shop/Http/Controllers/HomeController.php:22-100</a>", "middleware": "shop", "duration": "6.8s", "peak_memory": "34MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1668229793 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1668229793\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1492144237 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1492144237\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1487282894 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"898 characters\">_ss_s_uid=df387fd27bfaa61c2967bdd458dc2053; beike_version={%22current%22:%*********%22%2C%22latest%22:%221.5.6%22%2C%22release_date%22:%222025-05-08%22%2C%22has_new_version%22:true}; XSRF-TOKEN=eyJpdiI6IkNrKzNoZm1SNEZMQXhUZnVnWjBjbUE9PSIsInZhbHVlIjoicC9Lb3hLODNBeW9NSHRCNnJBUGtKNjdaL3ZWdXpXSDNySWszNzBXbDA4ZFdUdGhIRndabFZKTFVSaWZpM1Z4aG1rSjRKVTJpdWxzcWl2NG1lMlh4VDhCR0Z4ZFN1SzZlK01RU09LZUwzUXk1ellMYStYUnRlY2VYeUdxVGp6TCsiLCJtYWMiOiJkYmNmMzQ4ODAxMmIwZWNiYjc4NWM4NWIyMGZkZjQ3MWU0MGQ1NDI5YWIxYTgxZTg2OGM2ZWE3YjQyYmIxZmMzIiwidGFnIjoiIn0%3D; shopleade_session=eyJpdiI6IjkyUWlnd1JxS0plYkpXR25VZ2dKYUE9PSIsInZhbHVlIjoia0R2L29KWjZIUVYwRnl2UlFHRFFIUjFDa2F3RWY4YTlSZkZvT012TlhhcDJGYW15cjhjeVc1L1J4a2RNTGo5NTZqZnY5Vnk4TTlHTWlham5GcW9Wbzl1dzJZQ0FaUlRRYXlEY1k3Yy8weHJFTXBpa3ZFV0haanlBZHdpbEUzTFEiLCJtYWMiOiJmMWYzZjg0NTI4ODA2YWU2YzQ1MjZkZTc1M2ZkOWNhNmJjNjBhOWFlNTBjNDdiYTgxZTIzMDU2YTI4MDhhNjRiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">zh-CN,zh;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">newshop.shopleade.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1487282894\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-868118634 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_ss_s_uid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>beike_version</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">O4jG04i8Bcl9bpHOIqVGqqnZWApkEJZpBCBit84f</span>\"\n  \"<span class=sf-dump-key>shopleade_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YkGz0mA2y0BMpk6lkQ8Dkf8G3sQ6bHa4Ejf6HE7d</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-868118634\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2082689918 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 09 Sep 2025 01:27:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"455 characters\">XSRF-TOKEN=eyJpdiI6ImV6c29BL0FWQkdIYWZNQ0ZxejRDTVE9PSIsInZhbHVlIjoiVG92TGFCZlZGYllFa2xJQUJKT0tHR0hYNmtiUmc5Q250Ull4clFTS2NzM3JPRDBuUzMzeFVjckYreCtUYnQyaXpqU0o2NzJOZU4yNHRCV09zRUowRWhEMisrdGx3ZzdCQ0FBVFFsSUV5TXZFVllqUzRWaklJMlNFcitSZEVTVXAiLCJtYWMiOiIxOTExZWU1ZDExODhlMTFjNTUxYzI4ZjVjOTJiNTViMjhjOTgxMWVmMWY5M2JjZTYwZmNhMjU0ZDk0MmRkMzZjIiwidGFnIjoiIn0%3D; expires=Thu, 09 Oct 2025 01:27:07 GMT; Max-Age=2592000; path=/; domain=.shopleade.test; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"472 characters\">shopleade_session=eyJpdiI6IlhPQm5QTGVKSVFKKzY2OWQzSGNiNHc9PSIsInZhbHVlIjoicU5RMW01cmJuZG9Ic01mVU5LY1M3eTRhTEJqcldFWFFBeC9OUWQzVVV6QkRyTlFSNkgyMWxHdm5ZUVBKL21vbkszWTN0OFdNMUp2NzhRS1poYkR5UHpCa1J1TGM2ak5WT3dnOGdqMkl4dzRqYWIzb0daZWdOT2NqN25DekNGTEUiLCJtYWMiOiIxNjExOGQ1YzY1OGY5YjJkNmQ0Njc5YjUxZmNhNWZkNTQ2ZjM0Yzg3ZjVhNDhlZTNlMjQwZGUyNWNiZTNmYzVhIiwidGFnIjoiIn0%3D; expires=Thu, 09 Oct 2025 01:27:07 GMT; Max-Age=2592000; path=/; domain=.shopleade.test; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"424 characters\">XSRF-TOKEN=eyJpdiI6ImV6c29BL0FWQkdIYWZNQ0ZxejRDTVE9PSIsInZhbHVlIjoiVG92TGFCZlZGYllFa2xJQUJKT0tHR0hYNmtiUmc5Q250Ull4clFTS2NzM3JPRDBuUzMzeFVjckYreCtUYnQyaXpqU0o2NzJOZU4yNHRCV09zRUowRWhEMisrdGx3ZzdCQ0FBVFFsSUV5TXZFVllqUzRWaklJMlNFcitSZEVTVXAiLCJtYWMiOiIxOTExZWU1ZDExODhlMTFjNTUxYzI4ZjVjOTJiNTViMjhjOTgxMWVmMWY5M2JjZTYwZmNhMjU0ZDk0MmRkMzZjIiwidGFnIjoiIn0%3D; expires=Thu, 09-Oct-2025 01:27:07 GMT; domain=.shopleade.test; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">shopleade_session=eyJpdiI6IlhPQm5QTGVKSVFKKzY2OWQzSGNiNHc9PSIsInZhbHVlIjoicU5RMW01cmJuZG9Ic01mVU5LY1M3eTRhTEJqcldFWFFBeC9OUWQzVVV6QkRyTlFSNkgyMWxHdm5ZUVBKL21vbkszWTN0OFdNMUp2NzhRS1poYkR5UHpCa1J1TGM2ak5WT3dnOGdqMkl4dzRqYWIzb0daZWdOT2NqN25DekNGTEUiLCJtYWMiOiIxNjExOGQ1YzY1OGY5YjJkNmQ0Njc5YjUxZmNhNWZkNTQ2ZjM0Yzg3ZjVhNDhlZTNlMjQwZGUyNWNiZTNmYzVhIiwidGFnIjoiIn0%3D; expires=Thu, 09-Oct-2025 01:27:07 GMT; domain=.shopleade.test; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2082689918\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-169298693 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">O4jG04i8Bcl9bpHOIqVGqqnZWApkEJZpBCBit84f</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"5 characters\">zh_cn</span>\"\n  \"<span class=sf-dump-key>login_web_shop_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>63</span>\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://newshop.shopleade.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://jiuyia.shopleade.test/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>histories</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">admin.theme.index</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"19 characters\">admin.plugins.index</span>\"\n    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">admin.home.index</span>\"\n    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"20 characters\">admin.products.index</span>\"\n    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"19 characters\">admin.plugins.theme</span>\"\n    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"20 characters\">admin.settings.index</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>saas_front_locale</span>\" => \"<span class=sf-dump-str title=\"5 characters\">zh_cn</span>\"\n  \"<span class=sf-dump-key>login_web_saas_user_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>login_web_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">USD</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-169298693\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://newshop.shopleade.test", "action_name": "shop.home.index", "controller_action": "Beike\\Shop\\Http\\Controllers\\HomeController@index"}, "badge": null}}