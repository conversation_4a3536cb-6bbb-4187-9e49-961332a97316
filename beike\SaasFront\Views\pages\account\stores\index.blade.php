@extends('saas_front::layout.master')

{{-- @section('title', '')) --}}


@section('body-class', 'page-account')

@section('content')
    <section class="main">
        <div class="container">
            <div class="row">
                <div class="col user-left">
                    @include('saas_front::pages.account.shared.menu')
                </div>
                <div class="col  user-right">
                    <div class="card h-min-600 account-main-card">
                        <div class="card-body p-4">
                            @if (session()->has('error'))
                                <x-saas-alert type="danger" msg="{{ session('error') }}" class="mt-4" />
                            @endif

                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div class="account-title">{{ trans('saas_front::account.store_management') }}</div>
                                {{-- 当前客户创建的店铺数小于于系统设置的base.store_free_count时，显示按钮“创建试用店铺 2/3“，点击跳转到创建页面， 否则显示”购买开店套餐“，点击跳转到选择套餐购买页面 --}}
                                @if ($owns_count >= system_setting('base.store_free_count'))
                                    <a href="{{ saas_front_route('account.combos.index') }}"
                                        class="btn btn-primary">{{ __('saas_front::stores.buy_store_combo') }}</a>
                                @else
                                    <a href="{{ saas_front_route('account.stores.create', ['version' => $version_free]) }}"
                                        class="btn btn-primary">{{ __('saas_front::stores.create_free') }}</a>
                                @endif
                            </div>
                            @if ($errors->has('error'))
                                <x-saas-alert type="danger" msg="{{ $errors->first('error') }}" class="mt-4" />
                            @endif

                            @if (session()->has('success'))
                                <x-saas-alert type="success" msg="{{ session('success') }}" class="mt-4" />
                            @endif

                            <div class="">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>{{ __('saas_front::stores.version') }}</th>
                                            <th>{{ __('saas_front::stores.text1') }}</th>
                                            <th>{{ __('saas_front::stores.temp_domain') }}</th>
                                            <th>{{ __('saas_front::stores.status') }}</th>
                                            <th>{{ __('saas_front::stores.created_at') }}</th>
                                            <th>{{ __('saas_front::stores.expired_at') }}</th>
                                            <th>{{ __('saas_front::stores.ssl') }}</th>
                                            <th>{{ __('saas_front::stores.action') }}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($stores as $item)
                                            <tr>
                                                <td>{{ $item->id }}</td>
                                                <td>{{ $item->combo->description->name ?? '' }}</td>
                                                <td
                                                    data-url="{{ saas_front_route('account.stores.bind_domain', $item->id) }}">
                                                    @if ($item->domain)
                                                        {{ $item->domain }}
                                                    @else
                                                        {{ __('saas_front::stores.text4') }}
                                                    @endif
                                                </td>
                                                <td><a target="_blank" style="font-size: 12px" href="https://{{ $item->temp_domain . '.' . env('BEIKE_BASE_DOMAIN') }}">{{ $item->temp_domain . '.' . env('BEIKE_BASE_DOMAIN') }}</a></td>
                                                <td>{{ $item->status ? __('saas_front::common.enabled') : __('saas_front::common.disabled') }}
                                                </td>
                                                <td>{{ $item->created_at }}</td>
                                                <td>{{ $item->expired_at }}</td>
                                                <td>{{ $item->has_ssl }}</td>
                                                <td>
                                                    @if ($item->domain)
                                                        <a href="#"
                                                            class="cursor-pointer edit-domain btn btn-outline-primary btn-sm"
                                                            data-domain="{{ $item->domain }}"
                                                            data-temp-domain="{{ env('STORE_CNAME_VALUE', $item->temp_domain . '.' . env('BEIKE_BASE_DOMAIN')) }}"
                                                            data-id="{{ $item->id }}">{{ __('saas_front::stores.text3') }}</a>
                                                    @else
                                                        <a href="#"
                                                            class="cursor-pointer edit-domain btn btn-outline-primary btn-sm"
                                                            data-domain="{{ $item->domain }}"
                                                            data-temp-domain="{{ env('STORE_CNAME_VALUE', $item->temp_domain . '.' . env('BEIKE_BASE_DOMAIN')) }}"
                                                            data-id="{{ $item->id }}">{{ __('saas_front::stores.text2') }}</a>
                                                    @endif
                                                    <a href="{{ $item->admin_url }}" target="_blank"
                                                        class="btn btn-outline-primary btn-sm">{{ __('saas_front::stores.text5') }}</a>
                                                    <a href="{{ route('front.account.combos.index', 'store_id=' . $item->id) }}"
                                                        class="btn btn-outline-primary btn-sm">{{ __('saas_front::common.button.renew') }}</a>
                                                    @if ($item->domain && !$item->is_has_ssl)
                                                        <button type="button"
                                                            class="btn btn-sm btn-outline-primary start-ssl"
                                                            data-url="{{ saas_front_route('account.stores.ssl_enable', ['store_id' => $item->id]) }}">{{ __('saas_front::stores.ssl') }}</button>
                                                    @endif
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="modal fade" id="edit-domain-dialog" tabindex="-1" aria-labelledby="edit-domainLabel"
            aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <form action="" id="domain-form" method="POST" class="needs-validation" novalidate method="POST">
                        @csrf
                        <div class="modal-header">
                            <h1 class="modal-title fs-5" id="edit-domainLabel">{{ __('saas_front::stores.text2') }}</h1>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3 position-relative">
                                <label for="domain" class="form-label">{{ __('saas_front::stores.text2') }}</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" name="domain" required
                                        placeholder="{{ __('saas_front::stores.domain') }}">
                                    <span class="input-group-text" data-bs-toggle="tooltip" data-bs-placement="top"
                                        title="{{ __('saas_front::stores.text11') }}">
                                        <i class="bi bi-question-circle"></i>
                                    </span>
                                </div>
                                <div class="invalid-feedback">
                                    {{ __('common.error_required', ['name' => __('saas_front::stores.domain')]) }}
                                </div>
                            </div>
                            <div><a target="_blank"
                                    href="https://helper.shopleade.com/middle/domainname/{{ app()->getLocale() }}">{{ __('saas_front::stores.DNS_helper') }}</a>
                            </div>
                         
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary"
                                data-bs-dismiss="modal">{{ __('common.cancel') }}</button>
                            <button type="submit" class="btn btn-primary">{{ __('common.confirm') }}</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="coupon-dialog">
            <div class="coupon-overlay" style="display: none;">
                <div class="coupon-modal">
                    <div class="coupon-close">
                        <i class="bi bi-x-lg"></i>
                    </div>


                    <div class="coupon-stage-1">
                        <div class="coupon-icon">
                            <i class="bi bi-gift"></i>
                        </div>
                        <div class="coupon-welcome-title">{{ __('saas_front::account.text1') }}</div>
                        <div class="coupon-welcome-desc">{{ __('saas_front::account.text2') }}</div>
                        <div class="coupon-actions">
                            <button class="coupon-btn-receive stage-1-btn">
                                <span class="btn-text">{{ __('saas_front::account.text3') }}</span>
                                <span class="btn-loading"
                                    style="display: none;">{{ __('saas_front::account.text4') }}</span>
                            </button>
                        </div>
                    </div>

                    <!-- 第二阶段：优惠券详情 -->
                    <div class="coupon-stage-2" style="display: none;">
                        <div class="coupon-card">
                            <div class="coupon-left">
                                <div class="coupon-amount">
                                    <span class="currency">{{ __('saas_front::account.text5') }}</span>
                                    <span class="amount"></span>
                                    <span class="percent"></span>
                                </div>
                                <div class="coupon-type">{{ __('saas_front::account.text6') }}</div>
                            </div>

                            <div class="coupon-divider">
                                <div class="circle circle-top"></div>
                                <div class="dashed-line"></div>
                                <div class="circle circle-bottom"></div>
                            </div>

                            <div class="coupon-right">
                                <div class="coupon-title">{{ __('saas_front::account.text7') }}</div>
                                <div class="coupon-desc">{{ __('saas_front::account.text8') }}</div>
                                <div class="coupon-validity">
                                    {{ __('saas_front::account.text9') }}<span class="validity-date">2024-01-01 至
                                        2024-12-31</span>
                                </div>
                                <div class="coupon-code">
                                    {{ __('saas_front::account.text10') }}<span class="code-text">WELCOME50</span>
                                    <button class="copy-code-btn" title="{{ __('saas_front::account.text11') }}">
                                        <i class="bi bi-clipboard"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="coupon-success-container" style="margin-top: 20px; text-align: center; color: white;">
                            <h3 style="font-size: 20px; font-weight: bold; margin-bottom: 8px;">
                                {{ __('saas_front::account.text20') }}</h3>
                            <p style="font-size: 14px; opacity: 0.9; margin-bottom: 15px;">
                                {{ __('saas_front::account.text21') }}</p>
                            <div class="qr-code-wrapper"
                                style="display: inline-block; padding: 5px; background: white; border-radius: 12px; margin-bottom: 10px;">
                                <img src="{{ asset('/front/coupon/1.png') }}" alt="客服二维码"
                                    style="width: 120px; height: 120px; border-radius: 8px;">
                            </div>
                            <p style="font-size: 15px; font-weight: bold; margin-bottom: 4px;">
                                {{ __('saas_front::account.text22') }}</p>
                            <p style="font-size: 12px; opacity: 0.8;">{{ __('saas_front::account.text23') }}</p>
                        </div>
                    </div>

                    <!-- 装饰元素 -->
                    <div class="coupon-decoration">
                        <div class="star star-1">★</div>
                        <div class="star star-2">★</div>
                        <div class="star star-3">★</div>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection

@push('footer')
    <script>
        const userData = @json(@current_saas_front_user());
        $('.edit-domain').on('click', function(e) {
            e.preventDefault();
            const url = $(this).parent().data('url');
            const domain = $(this).data('domain');
            const tempDomain = $(this).data('temp-domain');

            $('#edit-domain-dialog').modal('show').find('form input[name="domain"]').val(domain);
            $('#edit-domain-dialog').find('form').attr('action', url);
            $('#edit-domain-dialog').find('.temp-domain').text(tempDomain);
        });

        $('.start-ssl').on('click', function(e) {
            e.preventDefault();
            const url = $(this).data('url');
            $http.post(url).then((res) => {
                if (res.status == 'success') {
                    layer.msg(res.message);
                    location.reload();
                }
            });
        });

        $('#domain-form').on('submit', function(e) {
            e.preventDefault();
            const url = $(this).attr('action');
            $http.post(url, $(this).serialize()).then((res) => {
                if (res.status == 'success') {
                    layer.msg(res.message);
                    location.reload();
                }
            });
        });

        const myModalEl = document.getElementById('edit-domain-dialog')
        myModalEl.addEventListener('hidden.bs.modal', event => {
            $('#domain-form').removeClass('was-validated');
        })

        myModalEl.addEventListener('shown.bs.modal', event => {
            var tooltipTriggerList = [].slice.call(myModalEl.querySelectorAll('[data-bs-toggle="tooltip"]'))
            var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl)
            })
        })

        $('.question-icon').on('click', function() {
            const lang = $('html').attr('lang');
            const img = lang === 'zh_cn' ? '{{ asset('image/bksaas/aliyun-cn.png') }}' :
                '{{ asset('image/bksaas/aliyun-en.png') }}';
            layer.open({
                type: 1,
                title: false,
                area: ['600px', '480px'],
                content: '<div class="d-flex justify-content-center align-items-center"></div><img src="' +
                    img + '" class="img-fluid" />'
            });
        });


        // 优惠券弹窗模块
        (function() {
            'use strict';

            // 优惠券弹窗相关函数
            function showCouponDialog(options = {}) {
                const defaults = {
                    amount: 50,
                    onReceive: null
                };

                const config = Object.assign(defaults, options);

                // 更新弹窗内容（不包含优惠码，因为还没获取）
                $('.coupon-dialog .amount').text(config.amount);
                $('.coupon-dialog .coupon-title').text(config.title);
                $('.coupon-dialog .coupon-desc').text(config.desc);
                $('.coupon-dialog .validity-date').text(config.startDate + ' {{ __('saas_front::account.text9') }} ' +
                    config.endDate);
                // 暂时不设置优惠码，等接口返回后再设置

                // 重置到第一阶段
                $('.coupon-stage-1').show();
                $('.coupon-stage-2').hide();

                // 显示弹窗
                $('.coupon-overlay').fadeIn(300);

                // 存储配置
                $('.coupon-overlay').data('config', config);
            }

            function hideCouponDialog() {
                $('.coupon-overlay').fadeOut(300);
            }

            async function receiveCoupon() {
                const $btn = $('.stage-1-btn');
                const $btnText = $btn.find('.btn-text');
                const $btnLoading = $btn.find('.btn-loading');
                const config = $('.coupon-overlay').data('config');

                // 显示加载状态
                $btn.prop('disabled', true);
                $btnText.hide();
                $btnLoading.show();

                try {
                    // 执行回调函数获取优惠码数据
                    if (typeof config.onReceive === 'function') {
                        const couponData = await config.onReceive();

                        if (couponData) {
                            // 更新优惠码信息
                            $('.coupon-dialog .amount').text(couponData.amount || config.amount);
                            $('.coupon-dialog .coupon-title').text(couponData.title || config.title);
                            $('.coupon-dialog .coupon-desc').text(couponData.desc || config.desc);
                            $('.coupon-dialog .validity-date').text(
                                (couponData.startDate || config.startDate) +
                                ' {{ __('saas_front::account.text9') }} ' +
                                (couponData.endDate || config.endDate)
                            );
                            $('.coupon-dialog .code-text').text(couponData.couponCode || config.couponCode);

                            // 恢复按钮状态
                            $btn.prop('disabled', false);
                            $btnText.show();
                            $btnLoading.hide();

                            // 切换到第二阶段
                            $('.coupon-stage-1').fadeOut(300, function() {
                                $('.coupon-stage-2').fadeIn(300);
                            });
                        } else {
                            throw new Error('{{ __('saas_front::account.text14') }}');
                        }
                    } else {
                        // 如果没有回调函数，直接切换到第二阶段
                        $btn.prop('disabled', false);
                        $btnText.show();
                        $btnLoading.hide();

                        $('.coupon-stage-1').fadeOut(300, function() {
                            $('.coupon-stage-2').fadeIn(300);
                        });
                    }
                } catch (error) {
                    // 请求异常处理
                    $btn.prop('disabled', false);
                    $btnText.show();
                    $btnLoading.hide();
                    layer.msg('{{ __('saas_front::account.text15') }}', {
                        icon: 2,
                        offset: 'auto'
                    });
                    console.error('{{ __('saas_front::account.text15') }}', error);
                }
            }

            function copyCode() {
                const codeText = $('.code-text').text();

                // 创建临时输入框来复制文本
                const tempInput = $('<input>');
                $('body').append(tempInput);
                tempInput.val(codeText).select();
                document.execCommand('copy');
                tempInput.remove();

                // 显示复制成功提示
                layer.msg('{{ __('saas_front::account.text16') }}', {
                    icon: 1
                });
            }

            $(function() {
                // 关闭按钮事件
                $('.coupon-close').on('click', function() {
                    hideCouponDialog();
                });

                // 点击遮罩层关闭
                $('.coupon-overlay').on('click', function(e) {
                    if (e.target === this) {
                        hideCouponDialog();
                    }
                });

                // 第一阶段领取按钮事件
                $('.stage-1-btn').on('click', function() {
                    receiveCoupon();
                });

                // 复制优惠码按钮事件
                $('.copy-code-btn').on('click', function() {
                    copyCode();
                });

                // 阻止弹窗内容区域的点击事件冒泡
                $('.coupon-modal').on('click', function(e) {
                    e.stopPropagation();
                });
            });

            // 暴露函数到全局作用域
            window.showCouponDialog = showCouponDialog;
            window.hideCouponDialog = hideCouponDialog;
            window.receiveCoupon = receiveCoupon;
            window.copyCode = copyCode;

        })();

        // 优惠券弹窗显示判断
        (function() {
            const couponConfig = {

                onReceive: async function() {
                    // 在这里调用你的接口
                    const res = await $http.post('/account/reg_coupon', {
                        code: '2508'
                    });
                    if (res.status === 'success') {
                        if (res.data.type == 'percent') {
                            $('.coupon-amount .percent').text('%');
                            $('.coupon-amount .currency').text('');
                        }
                        const obj = {
                            title: res.data.name + ' {{ __('saas_front::account.text7') }} ',
                            startDate: res.data.date_start,
                            endDate: res.data.date_end,
                            couponCode: res.data.code,
                            desc: ' {{ __('saas_front::account.text18') }} ' + Number(res.data.total)
                                .toFixed(2) + ' {{ __('saas_front::account.text19') }} ',
                            amount: res.data.discount
                        }
                        return obj;
                    } else {
                        throw new Error(res.message || ' {{ __('saas_front::account.text24') }} ');
                    }
                },

                coupon: userData?.reg_coupon_status,
                couponTime: '2025.08.01-2025.08.31'
            };

            // 检查是否应该显示优惠券弹窗
            function shouldShowCoupon(config) {
                // 检查 coupon 是否为 1
                if (config.coupon !== 1) {
                    return false;
                }

                // 检查 couponTime 是否在有效时间内
                if (config.couponTime) {
                    const timeRange = config.couponTime.split('-');
                    if (timeRange.length === 2) {
                        const startTime = new Date(timeRange[0].replace(/\./g, '/'));
                        const endTime = new Date(timeRange[1].replace(/\./g, '/'));
                        const currentTime = new Date();

                        // 设置时间到当天的开始和结束
                        startTime.setHours(0, 0, 0, 0);
                        endTime.setHours(23, 59, 59, 999);

                        if (currentTime < startTime || currentTime > endTime) {
                            return false;
                        }
                    }
                }

                return true;
            }

            // 如果满足条件则显示弹窗
            if (shouldShowCoupon(couponConfig)) {
                showCouponDialog(couponConfig);
            }
        })();
    </script>
@endpush

<style>
    .layui-layer {
        top: 50% !important;
        transform: translateY(-50%) !important;
    }

    /* 优惠券弹窗样式 */
    .coupon-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.6);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        animation: fadeIn 0.3s ease;
    }

    .coupon-modal {
        position: relative;
        background: linear-gradient(135deg, #1274FF, #4A90FF);
        border-radius: 20px;
        padding: 30px;
        max-width: 450px;
        width: 90%;
        box-shadow: 0 20px 40px rgba(18, 116, 255, 0.3);
        animation: slideUp 0.4s ease;
    }

    .coupon-close {
        position: absolute;
        top: 15px;
        right: 15px;
        width: 32px;
        height: 32px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        color: white;
        transition: all 0.3s ease;
        z-index: 10;
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .coupon-close:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: scale(1.1);
    }

    /* 第一阶段样式 */
    .coupon-stage-1 {
        text-align: center;
        color: white;
        padding: 20px 0;
    }

    .coupon-icon {
        font-size: 60px;
        margin-bottom: 20px;
        color: #FFD700;
        animation: bounce 2s infinite;
    }

    .coupon-welcome-title {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 10px;
    }

    .coupon-welcome-desc {
        font-size: 16px;
        opacity: 0.9;
        margin-bottom: 30px;
    }

    .stage-1-btn {
        background: linear-gradient(135deg, #FF6B35, #FF8E53);
        color: white;
        border: none;
        padding: 15px 40px;
        border-radius: 25px;
        font-size: 16px;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(255, 107, 53, 0.3);
    }

    .stage-1-btn:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(255, 107, 53, 0.4);
        background: linear-gradient(135deg, #e55a2b, #ff7a45);
    }

    .stage-1-btn:disabled {
        opacity: 0.7;
        cursor: not-allowed;
    }

    /* 第二阶段样式 */
    .coupon-stage-2 {
        animation: slideIn 0.5s ease;
    }

    .coupon-card {
        background: white;
        border-radius: 15px;
        display: flex;
        overflow: hidden;
        margin-bottom: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        position: relative;
    }

    .coupon-left {
        background: linear-gradient(135deg, #1274FF, #4A90FF);
        color: white;
        padding: 25px 20px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-width: 120px;
        position: relative;
    }

    .coupon-amount {
        display: flex;
        align-items: baseline;
        margin-bottom: 5px;
    }

    .currency.percent {
        font-size: 18px;
        font-weight: bold;
    }

    .amount {
        font-size: 36px;
        font-weight: bold;
        margin-left: 0 2px;
    }

    .coupon-type {
        font-size: 14px;
        opacity: 0.9;
    }

    .coupon-divider {
        position: relative;
        width: 2px;
        background: #f0f0f0;
    }

    .circle {
        position: absolute;
        width: 20px;
        height: 20px;
        background: linear-gradient(135deg, #1274FF, #4A90FF);
        border-radius: 50%;
        left: -9px;
    }

    .circle-top {
        top: -10px;
    }

    .circle-bottom {
        bottom: -10px;
    }

    .dashed-line {
        position: absolute;
        top: 10px;
        bottom: 10px;
        left: 0;
        width: 2px;
        background-image: linear-gradient(to bottom, #ddd 50%, transparent 50%);
        background-size: 2px 8px;
    }

    .coupon-right {
        flex: 1;
        padding: 25px 20px;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }

    .coupon-title {
        font-size: 18px;
        font-weight: bold;
        color: #333;
        margin-bottom: 8px;
        white-space: nowrap;
    }

    .coupon-desc {
        font-size: 14px;
        color: #666;
        margin-bottom: 12px;
    }

    .coupon-validity {
        font-size: 12px;
        color: #999;
        line-height: 1.4;
        margin-bottom: 10px;
    }

    .coupon-code {
        font-size: 12px;
        color: #333;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .code-text {
        background: #f8f9fa;
        padding: 4px 8px;
        border-radius: 4px;
        font-family: monospace;
        font-weight: bold;
        color: #1274FF;
        border: 1px solid #e9ecef;
    }

    .copy-code-btn {
        background: none;
        border: none;
        color: #1274FF;
        cursor: pointer;
        padding: 2px;
        border-radius: 3px;
        transition: all 0.2s ease;
    }

    .copy-code-btn:hover {
        background: #f8f9fa;
        color: #0d5ce6;
    }

    .coupon-success-msg {
        text-align: center;
        color: #28a745;
        font-size: 16px;
        font-weight: bold;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        background: rgba(255, 255, 255, 0.9);
        padding: 12px;
        border-radius: 10px;
    }

    .coupon-success-msg i {
        font-size: 18px;
    }

    .coupon-decoration {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        pointer-events: none;
        overflow: hidden;
    }

    .star {
        position: absolute;
        color: rgba(255, 255, 255, 0.3);
        font-size: 20px;
        animation: twinkle 2s infinite;
    }

    .star-1 {
        top: 20%;
        left: 10%;
        animation-delay: 0s;
    }

    .star-2 {
        top: 60%;
        right: 15%;
        animation-delay: 0.7s;
    }

    .star-3 {
        bottom: 25%;
        left: 20%;
        animation-delay: 1.4s;
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
        }

        to {
            opacity: 1;
        }
    }

    @keyframes slideUp {
        from {
            opacity: 0;
            transform: translateY(30px) scale(0.9);
        }

        to {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }

    @keyframes slideIn {
        from {
            opacity: 0;
            transform: translateX(20px);
        }

        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    @keyframes bounce {

        0%,
        20%,
        50%,
        80%,
        100% {
            transform: translateY(0);
        }

        40% {
            transform: translateY(-10px);
        }

        60% {
            transform: translateY(-5px);
        }
    }

    @keyframes twinkle {

        0%,
        100% {
            opacity: 0.3;
            transform: scale(1);
        }

        50% {
            opacity: 0.8;
            transform: scale(1.2);
        }
    }

    /* 响应式设计 */
    @media (max-width: 480px) {
        .coupon-modal {
            padding: 20px;
            margin: 20px;
        }

        .coupon-card {
            flex-direction: column;
        }

        .coupon-left {
            min-width: auto;
            flex-direction: row;
            justify-content: space-between;
            padding: 20px;
        }

        .coupon-right {
            padding: 20px;
        }

        .coupon-close {
            top: 10px;
            right: 10px;
            width: 28px;
            height: 28px;
        }

        .coupon-divider {
            height: 2px;
            width: 100%;
        }

        .circle {
            width: 20px;
            height: 20px;
            top: -9px;
        }

        .circle-top {
            left: 20%;
        }

        .circle-bottom {
            right: 20%;
            left: auto;
        }
    }
</style>
