<?php

namespace Beike\Admin\Http\Resources;

use Beike\SaasAdmin\Repositories\PluginPriceRepo;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PluginResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     * @throws \Exception
     */
    public function toArray($request): array
    {
        $storeId = current_store_id();

        $bought = boughtPlugin($this->code);
        $pluginPrice = PluginPriceRepo::findByColumn('plugin_code', $this->code);

        $price = '-';
        $name = $this->getLocaleName();
        $desc = $this->getLocaleDescription();
        if(!empty($pluginPrice)) {
            $name = $pluginPrice->description->title ?? '';
            $desc = $pluginPrice->description->summary ?? '';
            if($pluginPrice->pay_type == 1) {
                $price = $pluginPrice ? saas_currency_format($pluginPrice->price) : '-';
            } else {
                $price = '周期消费';
            }
        }
        return [
            'code'        => $this->code,
            'name'        => $name,
            'description' => $desc,
            'path'        => $this->path,
            'version'     => $this->version,
            'dir_name'    => $this->dirName,
            'type'        => $this->type,
            'type_format' => trans('admin/plugin.' . $this->type),
            'icon'        => plugin_resize($this->code, $this->icon),
            'author'      => $this->author,
            'status'      => $this->getStatus(),
            'installed'   => $this->getInstalled(),
            'bought'      => $bought ?: (!$pluginPrice || empty((float)$pluginPrice->price)),
            'price'       => $price,
            'checkout_url' => config('app.url') . "/account/plugin_orders/checkout/{$storeId}/{$this->code}",
            'edit_url'    => $this->getEditUrl(),
            'pay_type'    => $pluginPrice ? $pluginPrice->pay_type : 1,
            'list'        => $pluginPrice ? $pluginPrice->list : [],
        ];
    }
}
